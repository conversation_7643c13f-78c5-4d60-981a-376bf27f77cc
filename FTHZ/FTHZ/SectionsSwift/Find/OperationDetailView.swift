class OperationDetailView: UIView {
  private let titleL: UILabel = {
    let label = UILabel()
    label.font = .ft_SourceHanSerif_Blod_24
    label.textColor = .fz_HighBlackColor
    label.numberOfLines = 0
    return label
  }()

  private let timeL: UILabel = {
    let label = UILabel()
    label.font = .ft_SourceHanserifSC_Normal_12
    label.textColor = .fz_lightBlackColor
    return label
  }()

  private let photoView: HZPhotoGroupAbbreviation = {
    let view = HZPhotoGroupAbbreviation()
    view.clipsToBounds = true
    return view
  }()

  private let singleImageView: UIImageView = {
    let imageView = UIImageView()
    imageView.contentMode = .scaleAspectFill
    imageView.layer.cornerRadius = 8
    imageView.clipsToBounds = true
    return imageView
  }()

  private let contentL: UILabel = {
    let label = UILabel()
    label.font = .ft_SourceHanserifSC_Blod_16
    label.textColor = .darkGray
    label.numberOfLines = 0
    return label
  }()

  var detailData: OperationItemResult? {
    didSet {
      setupData()
    }
  }

  override init(frame: CGRect) {
    super.init(frame: frame)
    setupSubviews()
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  private func setupSubviews() {
    backgroundColor = .white
    addSubview(titleL)
    addSubview(timeL)
    addSubview(photoView)
    addSubview(contentL)
    addSubview(singleImageView)
    setupInitialConstraints()
  }

  private func setupInitialConstraints() {
    titleL.snp.makeConstraints { make in
      make.top.equalToSuperview().offset(kRealWidth(20))
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.right.equalToSuperview().offset(-kRealWidth(24))
    }

    timeL.snp.makeConstraints { make in
      make.top.equalTo(titleL.snp.bottom).offset(kRealWidth(12))
      make.left.equalTo(titleL)
      make.right.lessThanOrEqualTo(titleL)  // 添加右侧约束
      make.height.greaterThanOrEqualTo(kRealWidth(17))  // 添加最小高度约束
    }

    photoView.isHidden = true
    singleImageView.isHidden = true
  }

  override var intrinsicContentSize: CGSize {
    let height = contentL.frame.maxY + kRealWidth(20)
    return CGSize(width: UIView.noIntrinsicMetric, height: height)
  }

  private func setupData() {
    guard let detailData = detailData else { return }
    titleL.text = detailData.title

    let content = detailData.content ?? ""
    let attributedString = NSMutableAttributedString(string: content)

    let fullRange = NSRange(location: 0, length: content.count)
    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = 6

    attributedString.addAttributes(
      [
        .font: UIFont.ft_SourceHanserifSC_Blod_16,
        .foregroundColor: UIColor.darkGray,
        .paragraphStyle: paragraphStyle,
      ], range: fullRange)

    if let styles = detailData.contentSet {
      for style in styles {
        let range = NSRange(location: Int(style.start), length: Int(style.end - style.start))
        if range.location + range.length <= content.count,
          let colorString = style.color,
          let color = UIColor(hexString: colorString)
        {
          attributedString.addAttribute(.foregroundColor, value: color, range: range)
        }
      }
    }

    contentL.attributedText = attributedString

    if let createTime = detailData.create_time {
      let date = Date(timeIntervalSince1970: TimeInterval(truncating: createTime))
      let formatter = DateFormatter()
      formatter.dateFormat = "yy年M月d日"
      timeL.text = formatter.string(from: date)
    } else {
      timeL.text = ""
    }

    let images = detailData.images ?? ""
    let imageUrls = images.components(separatedBy: ",")
      .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
      .filter { !$0.isEmpty }

    if !imageUrls.isEmpty {
      if imageUrls.count == 1 {
        setupSingleImage(with: imageUrls[0])
      } else {
        setupPhotoView(with: imageUrls)
      }
    } else {
      setupWithoutImages()
    }

    setNeedsLayout()
    layoutIfNeeded()
    invalidateIntrinsicContentSize()
  }

  private func setupSingleImage(with imageUrl: String) {
    singleImageView.isHidden = false
    photoView.isHidden = true

    if let url = URL(string: imageUrl) {
      singleImageView.sd_setImage(with: url)
    }

    singleImageView.snp.makeConstraints { make in
      make.top.equalTo(timeL.snp.bottom).offset(kRealWidth(20))
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.right.equalToSuperview().offset(-kRealWidth(24))
      make.height.equalTo(kRealWidth(180))
    }

    contentL.snp.makeConstraints { make in
      make.top.equalTo(singleImageView.snp.bottom).offset(kRealWidth(20))
      make.left.right.equalTo(titleL)
      make.bottom.equalToSuperview().offset(-kRealWidth(20))
    }
  }

  private func setupPhotoView(with imageUrls: [String]) {
    photoView.isHidden = false
    singleImageView.isHidden = true

    let margin = kRealWidth(24)
    let spacing = kRealWidth(4)
    let maxWidth = FZSCREEN_W - (margin * 2)

    photoView.urlArray = imageUrls

    var itemWidth: CGFloat = 0
    var totalHeight: CGFloat = 0

    switch imageUrls.count {
    case 2:
      itemWidth = (maxWidth - spacing) / 2
      totalHeight = itemWidth
      photoView.getNum(2, index: 2)

    case 4:
      itemWidth = (maxWidth - spacing) / 2
      totalHeight = (itemWidth * 2) + spacing
      photoView.getNum(4, index: 4)

    default:
      itemWidth = (maxWidth - spacing * 2) / 3
      let rows = ceil(Float(imageUrls.count) / 3.0)
      totalHeight = itemWidth * CGFloat(rows) + spacing * CGFloat(rows - 1)
      photoView.getNum(imageUrls.count, index: 1)
    }

    photoView.snp.makeConstraints { make in
      make.top.equalTo(timeL.snp.bottom).offset(kRealWidth(20))
      make.left.equalToSuperview().offset(margin)
      make.right.equalToSuperview().offset(-margin)
      make.height.equalTo(totalHeight)
    }

    contentL.snp.makeConstraints { make in
      make.top.equalTo(photoView.snp.bottom).offset(kRealWidth(20))
      make.left.right.equalTo(titleL)
      make.bottom.equalToSuperview().offset(-kRealWidth(20))
    }
  }

  private func setupWithoutImages() {
    photoView.isHidden = true
    singleImageView.isHidden = true

    contentL.snp.makeConstraints { make in
      make.top.equalTo(timeL.snp.bottom).offset(kRealWidth(20))
      make.left.right.equalTo(titleL)
      make.bottom.equalToSuperview().offset(-kRealWidth(20))
    }
  }
}

extension UIColor {
  convenience init?(hexString: String) {
    var hex = hexString.trimmingCharacters(in: .whitespacesAndNewlines)
    if hex.hasPrefix("#") {
      hex.remove(at: hex.startIndex)
    }

    var rgb: UInt64 = 0
    Scanner(string: hex).scanHexInt64(&rgb)

    let r = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
    let g = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
    let b = CGFloat(rgb & 0x0000FF) / 255.0

    self.init(red: r, green: g, blue: b, alpha: 1.0)
  }
}
