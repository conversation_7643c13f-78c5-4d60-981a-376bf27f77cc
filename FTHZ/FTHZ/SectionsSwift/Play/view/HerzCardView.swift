import Foundation

class HerzCardView: HQIndexBannerSubview {
  var data: FTHZHeziRes?
  weak var parentVC: UIViewController?
  private let playerView: FTHZMusicPlayerView
  private lazy var contentBg = UIView.init()

  override func setupData(_ data: FTHZHeziRes!, hasTop top: Int32) {
    self.data = data

    for view in contentBg.subviews {
      view.removeFromSuperview()
    }

    var tempView: UIView!
    if let tags = data?.user.tag, !tags.isEmpty {
      for i in 0..<tags.count {
        let tag: TagModelResult = TagModelResult.mj_object(withKeyValues: data?.user.tag[i])
        let tagNameL = UILabelPadding.init()
        tagNameL.text = tag.name
        tagNameL.textColor = .black
        tagNameL.backgroundColor = .white
        tagNameL.font = .ft_SourceHanserifSC_Normal_10
        tagNameL.layer.cornerRadius = kRealWidth(4)
        tagNameL.layer.masksToBounds = true
        contentBg.addSubview(tagNameL)

        if tempView == nil {
          tagNameL.snp.makeConstraints { (make) in
            make.left.equalToSuperview().offset(kRealWidth(24))
            make.bottom.equalToSuperview().offset(kRealWidth(-60))
            make.height.equalTo(kRealWidth(16))
          }
        } else {
          if i % 2 > 0 {
            tagNameL.snp.makeConstraints { (make) in
              make.left.equalTo(tempView.snp_right).offset(kRealWidth(13))
              make.centerY.equalTo(tempView)
              make.height.equalTo(kRealWidth(16))
            }
          } else {
            tagNameL.snp.makeConstraints { (make) in
              make.left.equalToSuperview().offset(kRealWidth(24))
              make.top.equalTo(tempView.snp_bottom).offset(12)
              make.height.equalTo(kRealWidth(16))
            }
          }
        }

        tempView = tagNameL
      }
    }

    let sexMark = UIView.init()
    sexMark.backgroundColor = data?.user.gender == "1" ? .fz_skyBlueColor : .fz_femalePinkColor
    sexMark.layer.cornerRadius = kRealWidth(6)
    contentBg.addSubview(sexMark)

    let avatarIV = UIImageView.init()
    avatarIV.contentMode = .scaleAspectFill
    avatarIV.layer.cornerRadius = kRealWidth(26)
    avatarIV.layer.masksToBounds = true
    avatarIV.isUserInteractionEnabled = true
    let tapGesture = UITapGestureRecognizer(target: self, action: #selector(avatarTapped))
    avatarIV.addGestureRecognizer(tapGesture)

    let avatarBgView = UIView.init()
    contentBg.addSubview(avatarBgView)
    avatarBgView.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(24))
      make.bottom.equalToSuperview().offset(kRealWidth(-88))
      make.size.equalTo(CGSize(width: kRealWidth(52), height: kRealWidth(52)))
    }

    avatarBgView.addSubview(avatarIV)
    avatarIV.snp.makeConstraints { (make) in
      make.edges.equalToSuperview()
    }

    avatarIV.applyshadowWithCorner(containerView: avatarBgView, cornerRadious: kRealWidth(26))

    let userNameL = UILabelPadding.init()
    userNameL.text = data?.user.nickname
    userNameL.textColor = .fz_grayBlueColor
    userNameL.backgroundColor = .white
    userNameL.font = .ft_SourceHanSerif_Blod_14
    userNameL.layer.cornerRadius = kRealWidth(10)
    userNameL.layer.masksToBounds = true
    contentBg.addSubview(userNameL)

    let herzL = UILabel.init()
    if let hertz = data?.user.hertz, !hertz.isEmpty {
      herzL.text = hertz + "Hz"
    } else {
      herzL.text = ""
    }
    herzL.textColor = .white
    herzL.font = .ft_Din_Bold_12
    contentBg.addSubview(herzL)

    userNameL.snp.makeConstraints { (make) in
      make.left.equalTo(avatarBgView.snp.right).offset(kRealWidth(12))
      make.top.equalTo(avatarBgView.snp.top).offset(kRealWidth(5))
      make.height.equalTo(kRealWidth(20))
    }

    sexMark.snp.makeConstraints { (make) in
      make.left.equalTo(avatarBgView.snp.right).offset(kRealWidth(12))
      make.bottom.equalTo(avatarBgView.snp.bottom).offset(kRealWidth(-5))
      make.size.equalTo(CGSize.init(width: kRealWidth(12), height: kRealWidth(12)))
    }

    herzL.snp.makeConstraints { (make) in
      make.left.equalTo(sexMark.snp.right).offset(kRealWidth(8))
      make.centerY.equalTo(sexMark)
    }

    if let avatarUrl = data?.user.avatar {
      avatarIV.netImg(avatarUrl, EMPTY_ICON)
    }

    let contentL = UILabel.init()
    contentL.font = .ft_SourceHanserifSC_Normal_14
    contentL.textColor = .white
    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.lineSpacing = 4
    paragraphStyle.lineBreakMode = .byTruncatingTail

    let text = data.content.content.count > 0 ? data.content.content : "分享图片"
    let attributedString = NSAttributedString(
      string: text,
      attributes: [
        .paragraphStyle: paragraphStyle,
        .font: UIFont.ft_SourceHanserifSC_Normal_14,
        .foregroundColor: UIColor.white,
      ]
    )

    contentL.attributedText = attributedString
    let topMargin = 24

    var hasMusic = false
    if let musicData: MusicInfoData = data?.content.musicInfo {
      hasMusic = musicData.isKind(of: MusicInfoData.self)
      if hasMusic {
        playerView.setMusicInfo(musicData, uuid: data?.content.contentid, autoTrace: true)
      }
    }

    if data.content.images.count > 0 {
      contentL.numberOfLines = 2
    } else {
      contentL.numberOfLines = 5
    }
    contentBg.addSubview(contentL)
    contentL.snp.makeConstraints { (make) in
      make.left.equalToSuperview().offset(kRealWidth(25))
      make.right.equalToSuperview().offset(-kRealWidth(25))
      make.top.equalToSuperview().offset(topMargin)
    }

    var topView: UIView = contentL

    if data.content.images.count > 0,
      let urlArr = data?.content.images.components(separatedBy: ","),
      urlArr.count > 0
    {
      if urlArr.count > 1 {
        for i in 0...urlArr.count - 1 {
          let imgIV = UIImageView.init()
          imgIV.netImg(urlArr[i], EMPTY_ICON)
          imgIV.contentMode = .scaleAspectFill
          imgIV.layer.cornerRadius = kRealWidth(12)
          imgIV.layer.masksToBounds = true
          contentBg.addSubview(imgIV)
          topView = imgIV
          imgIV.snp.makeConstraints { (make) in
            make.left.equalToSuperview().offset(kRealWidth(CGFloat(25 + 83 * i)))
            make.top.equalTo(contentL.snp_bottom).offset(kRealWidth(20))
            make.size.equalTo(CGSize.init(width: kRealWidth(80), height: kRealWidth(80)))
          }
          if i == 2 && urlArr.count > 3 {
            let coverL = UILabel.init()
            coverL.text = "+\(urlArr.count - 3)"
            coverL.font = .ft_Din_Bold_32
            coverL.textAlignment = .center
            coverL.textColor = .white
            coverL.backgroundColor = .fz_tipTxtColor
            imgIV.addSubview(coverL)
            coverL.snp.makeConstraints { (make) in
              make.edges.equalToSuperview()
            }
            break
          }
        }
      } else {
        var height: CGFloat = 80.0
        var width: CGFloat = 80.0
        if data?.content.iconType == 1 {
          width = 240
          height = 60
        } else if data?.content.iconType == 1 {
          width = 60
          height = 100
        }
        let imgIV = UIImageView.init()
        imgIV.netImg(urlArr[0], EMPTY_ICON)
        imgIV.contentMode = .scaleAspectFill
        imgIV.layer.cornerRadius = kRealWidth(12)
        imgIV.layer.masksToBounds = true
        contentBg.addSubview(imgIV)
        imgIV.snp.makeConstraints { (make) in
          make.left.equalToSuperview().offset(kRealWidth(25))
          make.top.equalTo(contentL.snp_bottom).offset(kRealWidth(20))
          make.size.equalTo(CGSize.init(width: kRealWidth(width), height: kRealWidth(height)))
        }
        topView = imgIV
      }
    }

    if hasMusic {
      contentBg.addSubview(playerView)
      playerView.applyLightStyle()
      playerView.snp.remakeConstraints { (make) in
        make.left.equalTo(kRealWidth(25))
        make.right.equalToSuperview().offset(-kRealWidth(40))
        make.top.equalTo(topView.snp_bottom).offset(kRealWidth(20))
        make.height.equalTo(kRealWidth(60))
      }
      topView = playerView
    }
    playerView.isHidden = !hasMusic

    let moreBt = UIButton.init()
    let moreAttributedString = NSAttributedString(
      string: "查看更多",
      attributes: [
        NSAttributedString.Key.font: UIFont.ft_SourceHanserifSC_Normal_12,
        NSAttributedString.Key.foregroundColor: UIColor.fz_lightWiteColor,
        NSAttributedString.Key.underlineStyle: NSUnderlineStyle.single.rawValue,
        NSAttributedString.Key.underlineColor: UIColor.fz_lightWiteColor,
        NSAttributedString.Key.baselineOffset: 2,
      ]
    )
    moreBt.setAttributedTitle(moreAttributedString, for: .normal)

    moreBt.blockAction { [weak self] (button) in
      let vc = self?.parentContainerViewController
      let momentVC = FZMomentVC.init()
      momentVC.userId = data.user.userid
      momentVC.momentId = data.content.contentid
      vc?.navigationController?.pushViewController(momentVC, animated: true)
    }

    contentBg.addSubview(moreBt)
    var moreBtTopMargin: CGFloat = 36
    if FZSCREEN_H / FZSCREEN_W > 2 {
      moreBtTopMargin = 60
    }
    moreBt.snp.makeConstraints { (make) in
      make.top.equalTo(topView.snp_bottom).offset(kRealWidth(moreBtTopMargin))
      make.right.equalToSuperview().offset(-kRealWidth(32))
    }
  }

  func setupUI(size: CGSize) {
    contentBg.backgroundColor = .fz_HighBlackColor
    contentBg.layer.cornerRadius = kRealWidth(20)
    contentBg.layer.masksToBounds = true

    addSubview(contentBg)
    contentBg.snp.makeConstraints { (make) in
      make.center.equalToSuperview()
      make.width.equalTo(kRealWidth(280))
      make.height.equalTo(kRealWidth(500))
    }
  }

  override init(frame: CGRect) {
    playerView = FTHZMusicPlayerView.init()
    super.init(frame: frame)
    setupUI(size: frame.size)
  }

  required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }

  @objc private func avatarTapped() {
    guard let data = data else { return }
    let whaleVC = WhaleDetailVC.init()
    whaleVC.uid = data.user.userid
    parentVC?.navigationController?.pushViewController(whaleVC, animated: true)
  }
}

extension UIButton {
  func underline() {
    guard let text = self.titleLabel?.text else { return }
    let attributedString = NSMutableAttributedString(string: text)
    attributedString.addAttribute(
      NSAttributedString.Key.underlineStyle, value: NSUnderlineStyle.single.rawValue,
      range: NSRange(location: 0, length: text.count))
    self.setAttributedTitle(attributedString, for: .normal)
  }
}
