#import "UserInformationVC.h"
#import "CertificateModel.h"
#import "ChangeAccountVC.h"
#import "ChangeGenderVC.h"
#import "ChangeLikeTagVC.h"
#import "ChangeNameVC.h"
#import "ChangeSigelVC.h"
#import "ChangeTagVC.h"
#import "ChangeUserVC.h"
#import "RateAttributeModel.h"
#import "TZImagePickerManager.h"
#import "UserInformationTableViewCell.h"
#import "UserUserinfoModel.h"
// #import "VTBBirthdayPicker.h"
#import "UIImage+Wechat.h"
#import <Photos/Photos.h>
#import <QiniuSDK.h>
#import <objc/runtime.h>

#define UserInformationTableViewCellID @"UserInformationTableViewCellID"

@interface UserInformationVC () <UINavigationControllerDelegate,
                                 UITableViewDelegate, UITableViewDataSource> {
  NSString *selectBirthday;
  NSString *selectImage;
}
@property(nonatomic, strong) UITableView *tableView;
@property(nonatomic, strong) UIImage *willUpIMage;
@property(nonatomic, strong) NSArray *dataSource;
@property(nonatomic, strong) UIImageView *iconIamgeView;
@property(nonatomic, strong) NSString *imageToken;
@property(nonatomic, strong) NSMutableArray *tagArray; // 给到下一页的
@end

@implementation UserInformationVC
- (void)loadQNtoken {
  __weak typeof(self) wSelf = self;
  [HUD show];
  [CertificateModel getCertificateModel:@"1"
      image_name:@""
      success:^(NSDictionary *resultObject) {
        CertificateModel *member =
            [CertificateModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          CertificateModelResult *da = [CertificateModelResult
              mj_objectWithKeyValues:[member.data objectAtIndex:0]];
          wSelf.imageToken = da.token;
          [wSelf uploadImages:wSelf.willUpIMage];
        } else {
          [HUD dissmiss];
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [HUD dissmiss];
        [self showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

// 更新头像
- (void)loginLogoInfo {
  @weakify(self);
  [AccountManager
      updateUserInfoWithName:CurrentUser.nickname
                      gender:CurrentUser.gender
                      avatar:selectImage
                   signature:CurrentUser.signature
                       brith:CurrentUser.birth
                  completion:^(UserPersonResult *_Nullable user,
                               NSError *_Nullable error) {
                    [HUD dissmiss];
                    @strongify(self);
                    if (error) {
                      [self showToastFast:error.localizedDescription];
                      return;
                    }
                    [self getUserHeziInfo];
                    // 刷新头像 cell
                    dispatch_async(dispatch_get_main_queue(), ^{
                      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:2
                                                                  inSection:0];
                      [self.tableView
                          reloadRowsAtIndexPaths:@[ indexPath ]
                                withRowAnimation:UITableViewRowAnimationNone];
                    });
                  }];
}

- (void)loadBirthData {
  @weakify(self);
  [HUD show];
  [AccountManager
      updateUserInfoWithName:CurrentUser.nickname
                      gender:CurrentUser.gender
                      avatar:CurrentUser.avatar
                   signature:CurrentUser.signature
                       brith:[NemoUtil timeSwitchTimestamp:selectBirthday
                                              andFormatter:@"YY-M-d"]
                  completion:^(UserPersonResult *_Nullable user,
                               NSError *_Nullable error) {
                    [HUD dissmiss];
                    @strongify(self);
                    if (error) {
                      [self showToastFast:error.localizedDescription];
                      return;
                    }
                    [self getUserHeziInfo];
                    dispatch_async(dispatch_get_main_queue(), ^{
                      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:4
                                                                  inSection:0];
                      [self.tableView
                          reloadRowsAtIndexPaths:@[ indexPath ]
                                withRowAnimation:UITableViewRowAnimationNone];
                    });
                  }];
}

- (void)getUserHeziInfo {
  @weakify(self);
  [[FTHZAccountManager shared]
      syncUserInfoWithCompletion:^(UserPersonResult *_Nullable user,
                                   NSError *_Nullable error) {
        @strongify(self);
        [HUD dissmiss];
        if (error) {
          [self showToastFast:error.localizedDescription];
          return;
        }
        [NOTIFICENTER postNotificationName:ChangeMyUserInfoMation object:nil];
      }];
}

- (void)loadNextTagInfo:(NSInteger)type {
  if (!_tagArray) {
    _tagArray = [NSMutableArray new];
  }
  __weak typeof(self) wSelf = self;

  [RateAttributeModel
      getRateAttributeModel:@"1"
                    success:^(NSDictionary *resultObject) {
                      RateAttributeModel *member = [RateAttributeModel
                          mj_objectWithKeyValues:resultObject];
                      if ([member.success boolValue]) {
                        for (int i = 0; i < member.data.count; i++) {
                          TagModelResult *dy = [TagModelResult
                              mj_objectWithKeyValues:[member.data
                                                         objectAtIndex:i]];
                          [wSelf.tagArray addObject:dy];
                        }
                        [wSelf goNextWithUserINfo:type];
                      } else {
                      }
                    }
                    failure:^(NSError *requestErr){

                    }];
}

- (void)goNextWithUserINfo:(NSInteger)type {
  __weak typeof(self) wSelf = self;
  [GetUserinfoModel
      getUserUserinfo:^(NSDictionary *resultObject) {
        GetUserinfoModel *member =
            [GetUserinfoModel mj_objectWithKeyValues:resultObject];
        if ([member.success boolValue]) {
          UserPersonResult *useMember =
              [UserPersonResult mj_objectWithKeyValues:member.data.firstObject];
          NSMutableArray *arrM3 = [NSMutableArray new];
          NSMutableArray *arrM4 = [NSMutableArray new];
          NSMutableArray *arrM5 = [NSMutableArray new];
          NSMutableArray *arrM6 = [NSMutableArray new];
          for (int i = 0; i < useMember.paoLikeTag.count; i++) {
            TagModelResult *pTag = [TagModelResult
                mj_objectWithKeyValues:[useMember.paoLikeTag objectAtIndex:i]];
            [arrM3 addObject:pTag];
          }
          if (type == 4) {
            for (int i = 0; i < useMember.tag.count; i++) {
              TagModelResult *aTag = [TagModelResult
                  mj_objectWithKeyValues:[useMember.tag objectAtIndex:i]];
              [arrM3 addObject:aTag];
              [arrM4 addObject:aTag];
            }
            for (int i = 0; i < useMember.likeTag.count; i++) {
              TagModelResult *lTag = [TagModelResult
                  mj_objectWithKeyValues:[useMember.likeTag objectAtIndex:i]];
              [arrM5 addObject:lTag];
            }
            ChangeTagVC *vc = [[ChangeTagVC alloc] init];
            vc.tagNextArray = wSelf.tagArray;
            vc.heziNum = [CurrentUser.hertz doubleValue];
            vc.tagidStr = arrM3;
            vc.needdisArray = arrM4;
            vc.needlikedisArray = arrM5;
            [self.navigationController pushViewController:vc animated:YES];
          } else if (type == 5) {
            for (int i = 0; i < useMember.tag.count; i++) {
              TagModelResult *aTag = [TagModelResult
                  mj_objectWithKeyValues:[useMember.tag objectAtIndex:i]];
              [arrM3 addObject:aTag];
            }
            for (int i = 0; i < useMember.likeTag.count; i++) {
              TagModelResult *lTag = [TagModelResult
                  mj_objectWithKeyValues:[useMember.likeTag objectAtIndex:i]];
              [arrM5 addObject:lTag];
              [arrM4 addObject:lTag];
            }
            ChangeLikeTagVC *vc = [[ChangeLikeTagVC alloc] init];
            vc.tagNextArray = wSelf.tagArray;
            vc.heziNum = [CurrentUser.hertz doubleValue];
            vc.tagidStr = arrM3;
            vc.needdisArray = arrM4;
            vc.needlikedisArray = arrM5;
            [self.navigationController pushViewController:vc animated:YES];
          } else if (type == 6) {
            for (int i = 0; i < useMember.tag.count; i++) {
              TagModelResult *aTag = [TagModelResult
                  mj_objectWithKeyValues:[useMember.tag objectAtIndex:i]];
              [arrM6 addObject:aTag];
            }
            for (int i = 0; i < useMember.likeTag.count; i++) {
              TagModelResult *lTag = [TagModelResult
                  mj_objectWithKeyValues:[useMember.likeTag objectAtIndex:i]];
              [arrM5 addObject:lTag];
            }
            ChangeUserVC *vc = [[ChangeUserVC alloc] init];
            vc.likePao = arrM3;
            vc.tagA = arrM6;
            vc.tagB = arrM5;
            vc.heziNum = [CurrentUser.hertz doubleValue];
            [self.navigationController pushViewController:vc animated:YES];
          }
        } else {
          [self showToastFast:member.msg];
        }
      }
      failure:^(NSError *requestErr) {
        [wSelf showToastFast:@"数据有误,请检查网络后重试"];
      }];
}

- (void)viewDidLoad {
  [super viewDidLoad];
  @weakify(self);

  MASViewAttribute *bottom = [self setTitleForV199:@"编辑资料"];
  _dataSource = [NSArray array];
  _dataSource = @[
    @"昵称:", @"账号名:", @"头像:", @"性别:", @"生日:", @"我是什么样的:",
    @"想认识的人:", @"我喜欢什么:", @"简介:"
  ];
  [self.safeContentView addSubview:self.tableView];
  [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.top.equalTo(bottom).offset(20 * kWidthFactor);
    make.left.right.bottom.equalTo(self.safeContentView);
  }];
  [NOTIFICENTER addObserver:self
                   selector:@selector(tableViewloadData:)
                       name:ChangeMyUserInfoMation
                     object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
  [super viewWillAppear:animated];
  [AppConfig statusbarStyle:YES];
}

- (void)tableViewloadData:(NSNotification *)notification {
  dispatch_async(dispatch_get_main_queue(), ^{
    NSNumber *row = notification.object;
    if (row) {
      NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row.integerValue
                                                  inSection:0];
      [self.tableView reloadRowsAtIndexPaths:@[ indexPath ]
                            withRowAnimation:UITableViewRowAnimationNone];
    }
  });
}
- (UITableView *)tableView {
  if (!_tableView) {
    _tableView = [[UITableView alloc] initWithFrame:(CGRectZero)
                                              style:(UITableViewStyleGrouped)];
    _tableView.backgroundColor = KColor_White;
    _tableView.delegate = self;
    _tableView.dataSource = self;
    [_tableView registerClass:[UserInformationTableViewCell class]
        forCellReuseIdentifier:UserInformationTableViewCellID];
    _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
  }
  return _tableView;
}

- (void)backAction {
  [super backAction];
  [[NSNotificationCenter defaultCenter] postNotificationName:ReopenSideMenu
                                                      object:nil];
}
#pragma mark - Table view data source
- (CGFloat)tableView:(UITableView *)tableView
    heightForFooterInSection:(NSInteger)section {
  return 0.01f;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForHeaderInSection:(NSInteger)section {
  return 52 * kWidthFactor;
}

- (UIView *)tableView:(UITableView *)tableView
    viewForFooterInSection:(NSInteger)section {
  return [[UIView alloc] initWithFrame:CGRectZero];
}

- (UIView *)tableView:(UITableView *)tableView
    viewForHeaderInSection:(NSInteger)section {
  UIView *headerV = [[UIView alloc]
      initWithFrame:CGRectMake(0, 0, kMainWidth, 52 * kWidthFactor)];
  NSString *likeStr =
      [NSString stringWithFormat:@"欢迎你, %@ 的小鲸鱼", @"52Hz"];
  NSString *str001 = @"欢迎你,";
  NSString *str002 = @" 52Hz ";
  NSString *str003 = @"的小鲸鱼";
  NSMutableAttributedString *str =
      [[NSMutableAttributedString alloc] initWithString:likeStr];
  NSRange range1 = [[str string] rangeOfString:str001];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:18 * kWidthFactor
                                      weight:UIFontWeightMedium]
              range:range1];
  NSRange range3 = [[str string] rangeOfString:str002];
  [str addAttribute:NSFontAttributeName
              value:[UIFont fontWithName:@"AvenirNext-Heavy"
                                    size:20 * kWidthFactor]
              range:range3];
  NSRange range5 = [[str string] rangeOfString:str003];
  [str addAttribute:NSFontAttributeName
              value:[UIFont systemFontOfSize:18 * kWidthFactor
                                      weight:UIFontWeightMedium]
              range:range5];
  UIColor *genderColor = [CurrentUser.gender isEqualToString:@"1"]
                             ? KColor_switchLightBlue
                             : KColor_switchLightPink;
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_titleDarkGray
              range:range1];
  [str addAttribute:NSForegroundColorAttributeName
              value:genderColor
              range:range3];
  [str addAttribute:NSForegroundColorAttributeName
              value:KColor_titleDarkGray
              range:range5];

  UILabel *headerLabel = [[UILabel alloc] init];
  headerLabel.attributedText = str;
  [headerV addSubview:headerLabel];
  [headerLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(headerV).offset(32 * kWidthFactor);
    make.top.equalTo(headerV).offset(16 * kWidthFactor);
    make.size.mas_equalTo(
        CGSizeMake(kMainWidth - 32 * kWidthFactor, 25 * kWidthFactor));
  }];
  return headerV;
}

- (CGFloat)tableView:(UITableView *)tableView
    heightForRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row >= 0 && indexPath.row < 5) {
    return 45 * kWidthFactor;
  } else if (indexPath.row >= 5 && indexPath.row <= 6) {
    return 108 * kWidthFactor;
  } else if (indexPath.row == 7) {
    CGSize likeTemp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kWidthFactor]
                             width:kMainWidth - 64 * kWidthFactor
                         heightMax:kMainHeight
                           content:@"哈哈的/散搭几乎都是/爱神的箭爱仕达/"
                                   @"爱神的箭拉速度快/萨克的静安寺/大空间大/"
                                   @"的圣诞节开始"];
    return likeTemp.height + 50 * kWidthFactor;
  } else if (indexPath.row == 8) {
    CGSize likeTemp = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kWidthFactor]
                             width:kMainWidth - 64 * kWidthFactor
                         heightMax:kMainHeight
                           content:CurrentUser.signature];
    return likeTemp.height + 50 * kWidthFactor;
  }
  return 48.0;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
  return 1;
}

- (NSInteger)tableView:(UITableView *)tableView
    numberOfRowsInSection:(NSInteger)section {
  return _dataSource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView
         cellForRowAtIndexPath:(NSIndexPath *)indexPath {
  NSString *nametitle = [_dataSource objectAtIndex:indexPath.row];
  UserInformationTableViewCell *cell =
      (UserInformationTableViewCell *)[tableView
          dequeueReusableCellWithIdentifier:UserInformationTableViewCellID
                               forIndexPath:indexPath];
  if (!cell) {
    cell = [[UserInformationTableViewCell alloc]
          initWithStyle:(UITableViewCellStyleDefault)
        reuseIdentifier:UserInformationTableViewCellID];
  }
  cell.nameLabel.text = nametitle;
  if (indexPath.row == 0) {
    [cell loadType1:CurrentUser.nickname];
  } else if (indexPath.row == 1) {
    [cell loadType1:CurrentUser.account];
  } else if (indexPath.row == 3) {
    if ([CurrentUser.gender isEqualToString:@"1"]) {
      [cell loadType1:@"男"];
    } else {
      [cell loadType1:@"女"];
    }
  } else if (indexPath.row == 4) {
    [cell loadType1:[NemoUtil getYearMonthDayOfData:CurrentUser.birth]];
  } else if (indexPath.row == 2) {
    [cell loadType2];
  } else if (indexPath.row == 5) {
    [cell loadType3];
  } else if (indexPath.row == 6) {
    [cell loadType5];
  } else if (indexPath.row == 7) {
    [cell loadType6];
  } else if (indexPath.row == 8) {
    [cell loadType4];
  }
  return cell;
}

- (void)tableView:(UITableView *)tableView
    didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
  if (indexPath.row == 0) {
    ChangeNameVC *vc = [[ChangeNameVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
  } else if (indexPath.row == 1) {
    ChangeAccountVC *vc = [[ChangeAccountVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
  } else if (indexPath.row == 3) {
    ChangeGenderVC *vc = [[ChangeGenderVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
  } else if (indexPath.row == 4) {
    [self showBirthdayPicker];
  } else if (indexPath.row == 2) {
    [self takePhoto];
  } else if (indexPath.row == 5) {
    [self loadNextTagInfo:4];
  } else if (indexPath.row == 6) {
    [self loadNextTagInfo:5];
  } else if (indexPath.row == 8) {
    ChangeSigelVC *vc = [[ChangeSigelVC alloc] init];
    [self.navigationController pushViewController:vc animated:YES];
  } else if (indexPath.row == 7) {
    [self loadNextTagInfo:6];
  }

  [tableView deselectRowAtIndexPath:indexPath animated:YES];
}
#pragma mark BirthdayPickerDelegate

- (void)showBirthdayPicker {
  UIViewController *pickerVC = [[UIViewController alloc] init];
  pickerVC.modalPresentationStyle = UIModalPresentationCustom;

  UIView *containerView = [[UIView alloc] init];
  containerView.backgroundColor = KColor_DarkKeyboard;
  containerView.frame = CGRectMake(0, self.view.bounds.size.height - 280,
                                   self.view.bounds.size.width, 280);
  pickerVC.view = containerView;

  UIToolbar *toolbar = [[UIToolbar alloc] init];
  toolbar.barStyle = UIBarStyleDefault;
  toolbar.translucent = NO;
  toolbar.barTintColor = KColor_DarkKeyboard;
  toolbar.frame = CGRectMake(0, 0, containerView.frame.size.width, 44);

  UIBarButtonItem *cancelButton =
      [[UIBarButtonItem alloc] initWithTitle:@"取消"
                                       style:UIBarButtonItemStylePlain
                                      target:self
                                      action:@selector(dismissBirthdayPicker:)];
  [cancelButton
      setTitleTextAttributes:@{NSForegroundColorAttributeName : KColor_White}
                    forState:UIControlStateNormal];

  UIBarButtonItem *doneButton =
      [[UIBarButtonItem alloc] initWithTitle:@"确定"
                                       style:UIBarButtonItemStyleDone
                                      target:self
                                      action:@selector(doneBirthdayPicker:)];
  [doneButton
      setTitleTextAttributes:@{NSForegroundColorAttributeName : KColor_White}
                    forState:UIControlStateNormal];

  UIBarButtonItem *flexibleSpace = [[UIBarButtonItem alloc]
      initWithBarButtonSystemItem:UIBarButtonSystemItemFlexibleSpace
                           target:nil
                           action:nil];
  UILabel *titleLabel =
      [[UILabel alloc] initWithFrame:CGRectMake(0, 0, 200, 44)];
  titleLabel.text = @"选择生日";
  titleLabel.textAlignment = NSTextAlignmentCenter;
  titleLabel.textColor = KColor_White;
  titleLabel.font = [UIFont systemFontOfSize:16];
  UIBarButtonItem *titleButton =
      [[UIBarButtonItem alloc] initWithCustomView:titleLabel];

  toolbar.items =
      @[ cancelButton, flexibleSpace, titleButton, flexibleSpace, doneButton ];
  [containerView addSubview:toolbar];

  UIDatePicker *datePicker = [[UIDatePicker alloc] init];
  if (@available(iOS 13.4, *)) {
    datePicker.preferredDatePickerStyle = UIDatePickerStyleWheels;
  }
  datePicker.datePickerMode = UIDatePickerModeDate;
  datePicker.backgroundColor = KColor_DarkKeyboard;

  if (@available(iOS 14.0, *)) {
    datePicker.tintColor = UIColor.whiteColor;
    datePicker.overrideUserInterfaceStyle = UIUserInterfaceStyleDark;
  } else {
    datePicker.overrideUserInterfaceStyle = UIUserInterfaceStyleDark;
  }

  NSCalendar *calendar = [NSCalendar currentCalendar];
  NSDate *now = [NSDate date];
  NSDateComponents *minComponents = [[NSDateComponents alloc] init];
  [minComponents setYear:-100];
  NSDate *minDate = [calendar dateByAddingComponents:minComponents
                                              toDate:now
                                             options:0];
  datePicker.minimumDate = minDate;
  datePicker.maximumDate = now;

  if (![CurrentUser.birth isEqualToString:@""]) {
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yy年M月d日"];
    NSString *birthdayString =
        [NemoUtil getYearMonthDayWithLineOfData:CurrentUser.birth];
    NSDate *birthdayDate = [dateFormatter dateFromString:birthdayString];
    if (birthdayDate) {
      datePicker.date = birthdayDate;
    }
  }

  datePicker.frame = CGRectMake(0, 44, containerView.frame.size.width, 236);
  [containerView addSubview:datePicker];

  objc_setAssociatedObject(pickerVC, "datePicker", datePicker,
                           OBJC_ASSOCIATION_RETAIN_NONATOMIC);

  UIView *backgroundView = [[UIView alloc] initWithFrame:self.view.bounds];
  backgroundView.backgroundColor = [UIColor blackColor];
  backgroundView.alpha = 0;
  [self.view addSubview:backgroundView];

  UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
      initWithTarget:self
              action:@selector(dismissBirthdayPicker:)];
  [backgroundView addGestureRecognizer:tapGesture];

  objc_setAssociatedObject(pickerVC, "backgroundView", backgroundView,
                           OBJC_ASSOCIATION_RETAIN_NONATOMIC);

  [self addChildViewController:pickerVC];
  [self.view addSubview:pickerVC.view];

  pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
  [UIView animateWithDuration:0.3
                   animations:^{
                     pickerVC.view.transform = CGAffineTransformIdentity;
                     backgroundView.alpha = 0.4;
                   }];
}

- (void)dismissBirthdayPicker:(id)sender {
  UIViewController *pickerVC = self.childViewControllers.lastObject;
  UIView *backgroundView = objc_getAssociatedObject(pickerVC, "backgroundView");

  [UIView animateWithDuration:0.3
      animations:^{
        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        backgroundView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [pickerVC.view removeFromSuperview];
        [pickerVC removeFromParentViewController];
      }];
}

- (void)doneBirthdayPicker:(id)sender {
  UIViewController *pickerVC = self.childViewControllers.lastObject;
  UIDatePicker *datePicker = objc_getAssociatedObject(pickerVC, "datePicker");
  UIView *backgroundView = objc_getAssociatedObject(pickerVC, "backgroundView");

  NSDate *selectedDate = datePicker.date;
  NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
  [dateFormatter setDateFormat:@"yy-M-d"];
  NSString *birthdayString = [dateFormatter stringFromDate:selectedDate];

  selectBirthday = birthdayString;
  [self loadBirthData];

  [UIView animateWithDuration:0.3
      animations:^{
        pickerVC.view.transform = CGAffineTransformMakeTranslation(0, 280);
        backgroundView.alpha = 0;
      }
      completion:^(BOOL finished) {
        [backgroundView removeFromSuperview];
        [pickerVC.view removeFromSuperview];
        [pickerVC removeFromParentViewController];
      }];
}

- (void)takePhoto {
  [[TZImagePickerManager sharedManager]
      presentSinglePhotoPickerWithViewController:self
      allowCrop:YES
      delegate:nil
      completionHandler:^(NSArray<UIImage *> *_Nullable images,
                          NSArray<PHAsset *> *_Nullable assets,
                          BOOL isOriginal) {
        if (images.count > 0) {
          self.willUpIMage = images.firstObject;
          [self loadQNtoken];
        }
      }
      cancelHandler:^{
        // 用户取消选择
        [AppConfig statusbarStyle:YES];
      }];
}

- (void)uploadImages:(UIImage *)scaledImage {

  UIGraphicsBeginImageContext(CGSizeMake(512, 512));
  [scaledImage drawInRect:CGRectMake(0, 0, 512, 512)];

  __weak typeof(self) wSelf = self;
  QNConfiguration *config =
      [QNConfiguration build:^(QNConfigurationBuilder *builder) {
        builder.zone = [QNFixedZone zone0];
      }];
  QNUploadManager *upManager =
      [[QNUploadManager alloc] initWithConfiguration:config];
  QNUploadOption *option = [[QNUploadOption alloc]
      initWithProgressHandler:^(NSString *key, float percent){
      }];
  NSString *imgKey =
      [NSString stringWithFormat:@"%@%@", [NemoUtil randomString],
                                 [NemoUtil getNowTimeIntervalStr]];
  [upManager
       putFile:[self getImagePath:scaledImage]
           key:imgKey
         token:self.imageToken
      complete:^(QNResponseInfo *info, NSString *key, NSDictionary *resp) {
        if (info.ok) {
          self->selectImage = imgKey;
          [self loginLogoInfo];
        } else {
          [HUD dissmiss];
          [wSelf showToastFast:@"头像上传失败,请重试"];
        }
      }
        option:option];
}

- (NSString *)getImagePath:(UIImage *)Image {
  NSString *filePath = nil;
  NSData *data = nil;
  data = [Image wcTimelineCompress];

  NSString *DocumentsPath =
      [NSHomeDirectory() stringByAppendingPathComponent:@"Documents"];

  NSFileManager *fileManager = [NSFileManager defaultManager];

  [fileManager createDirectoryAtPath:DocumentsPath
         withIntermediateDirectories:YES
                          attributes:nil
                               error:nil];
  NSString *ImagePath = [[NSString alloc] initWithFormat:@"/theFirstImage.png"];
  [fileManager
      createFileAtPath:[DocumentsPath stringByAppendingString:ImagePath]
              contents:data
            attributes:nil];

  filePath =
      [[NSString alloc] initWithFormat:@"%@%@", DocumentsPath, ImagePath];
  return filePath;
}

@end
