#import "MsgDetailTableViewCell.h"
#import "CurrencyRootVC.h"
#import "HZPhotoBrowserConfig.h"
#import "HZPhotoGroupAbbreviation.h"

@interface MsgDetailTableViewCell ()
@property(nonatomic, strong) HZPhotoGroupAbbreviation *photoView;
@property(nonatomic, strong) UIView *dybgView;
@property(nonatomic, strong) UILabel *dyplLabel;
@property(nonatomic, strong) UILabel *dytitleLabel;
@property(nonatomic, strong) UILabel *dygrayLabel;
@property(nonatomic, strong) UILabel *dyfortitleLabel;
@property(nonatomic, strong) UIImageView *dyimgView;
@property(nonatomic, strong) UILabel *audioCallTipsL;

@end

@implementation MsgDetailTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {

    [self loadCellView];
    UILongPressGestureRecognizer *longPress =
        [[UILongPressGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(longPressed:)];
    [_bgView addGestureRecognizer:longPress];
    UILongPressGestureRecognizer *imgLongPress =
        [[UILongPressGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(longPressed:)];
    [_photoView addGestureRecognizer:imgLongPress];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)longPressed:(UILongPressGestureRecognizer *)longPress {
  switch (longPress.state) {
  case UIGestureRecognizerStateBegan: {
    [self showLongpressActionWithView:longPress.view];
  } break;

  default:
    break;
  }
}

- (void)showLongpressActionWithView:(UIView *)view {
  @weakify(self);
  FTHZPopMenuItem *item;
  if (view == self.photoView) {
    item = [FTHZPopMenuItem menuWithTitle:@"保存图片"
                                   action:^{
                                     @strongify(self);
                                     [self saveImage];
                                   }];
  } else if (view == self.bgView) {
    item = [FTHZPopMenuItem menuWithTitle:@"复制"
                                   action:^{
                                     @strongify(self);
                                     [UIPasteboard generalPasteboard].string =
                                         self.titleDetailsLabel.text;
                                   }];
  }
  if (!item) {
    return;
  }
  FTHZPopMenuView *menu = [[FTHZPopMenuView alloc] init];
  [menu setMenuItems:@[ item ]];
  [menu showInTargetRect:[view convertRect:view.bounds toView:self]
                  inView:self
          dismissHandler:nil];
}

- (void)saveImage {
  NSString *imageURL = self.photoView.urlArray.firstObject;
  if (!imageURL) {
    return;
  }
  NSURL *url = [NSURL URLWithString:imageURL];
  if (!url) {
    return;
  }
  [HUD show];
  @weakify(self);
  [[SDWebImageManager sharedManager].imageLoader requestImageWithURL:url
      options:0
      context:nil
      progress:^(NSInteger receivedSize, NSInteger expectedSize,
                 NSURL *_Nullable targetURL) {

      }
      completed:^(UIImage *_Nullable image, NSData *_Nullable data,
                  NSError *_Nullable error, BOOL finished) {
        [HUD dissmiss];
        @strongify(self);
        if (image) {
          UIImageWriteToSavedPhotosAlbum(
              image, self,
              @selector(image:didFinishSavingWithError:contextInfo:), NULL);
        } else if (error) {
          UIViewController *vc = [UIViewController topViewController];
          if (![vc isKindOfClass:[CurrencyRootVC class]]) {
            return;
          }
          [((CurrencyRootVC *)vc) showToast:error.localizedDescription];
        }
      }];
  //    [[SDWebImageDownloader sharedDownloader] downloadImageWithURL:url
  //    options:0 progress:nil completed:^(UIImage * _Nullable image, NSData *
  //    _Nullable data, NSError * _Nullable error, BOOL finished) {
  //        [HUD dissmiss];
  //        @strongify(self);
  //        if (image) {
  //            UIImageWriteToSavedPhotosAlbum(image, self,
  //            @selector(image:didFinishSavingWithError:contextInfo:), NULL);
  //        } else if (error) {
  //            UIViewController *vc = [UIViewController topViewController];
  //            if (![vc isKindOfClass:[CurrencyRootVC class]]) {
  //                return;
  //            }
  //            [((CurrencyRootVC *)vc) showToast:error.localizedDescription];
  //        }
  //
  //    }];
}

- (void)image:(UIImage *)image
    didFinishSavingWithError:(NSError *)error
                 contextInfo:(void *)contextInfo {
  CurrencyRootVC *vc = (CurrencyRootVC *)[UIViewController topViewController];
  if (![vc isKindOfClass:[CurrencyRootVC class]]) {
    return;
  }
  if (error) {
    [vc showToast:error.localizedDescription];
  } else {
    [vc showToast:HZPhotoBrowserSaveImageSuccessText];
  }
}

- (void)loadCellView {
  self.contentView.backgroundColor = KColor_LightGray;
  if (!_iconImage) {
    _iconImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(24 * kWidthFactor, 10 * kWidthFactor,
                                 20 * kWidthFactor, 20 * kWidthFactor)];
  }
  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_iconImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconImage.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _iconImage.bounds;
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  [self.contentView addSubview:_iconImage];

  _iconImage.userInteractionEnabled = YES;
  [_iconImage addGestureRecognizer:[[UITapGestureRecognizer alloc]
                                       initWithTarget:self
                                               action:@selector(tapAction:)]];

  if (!_bgView) {
    _bgView = [[UIView alloc] init];
  }
  _bgView.layer.masksToBounds = YES;
  _bgView.layer.cornerRadius = 3;

  if (!_titleDetailsLabel) {
    _titleDetailsLabel = [[UILabel alloc] init];
  }
  _titleDetailsLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  [_titleDetailsLabel setTextColor:KColor_Black];
  [_titleDetailsLabel setNumberOfLines:0];

  if (!_photoView) {
    _photoView = [[HZPhotoGroupAbbreviation alloc] init];
  }

  if (!_soundImageView) {
    _soundImageView = [[UIImageView alloc] init];
  }

  if (!_soundTimeLabel) {
    _soundTimeLabel = [[UILabel alloc] init];
  }
  _soundTimeLabel.font = DinCondensedBoldFont(12 * kWidthFactor);

  if (!_soundTagBtn) {
    _soundTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }

  if (!_dybgView) {
    _dybgView = [[UIView alloc] init];
  }
  _dybgView.layer.masksToBounds = YES;
  _dybgView.layer.cornerRadius = 2 * kWidthFactor;
  _dybgView.backgroundColor = KColor_White;
  [self.contentView addSubview:_dybgView];
  [_dybgView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.contentView.mas_right).offset(-54 * kWidthFactor);
    make.centerY.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(236 * kWidthFactor, 115 * kWidthFactor));
  }];

  if (!_dyplLabel) {
    _dyplLabel = [[UILabel alloc] init];
  }
  _dyplLabel.layer.masksToBounds = YES;
  _dyplLabel.layer.cornerRadius = 2 * kWidthFactor;
  _dyplLabel.backgroundColor = KColor_HighBlack;
  _dyplLabel.text = @"评论";
  _dyplLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  _dyplLabel.textColor = KColor_White;
  _dyplLabel.textAlignment = NSTextAlignmentCenter;
  [self.dybgView addSubview:_dyplLabel];
  [_dyplLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.dybgView).offset(12 * kWidthFactor);
    make.top.equalTo(self.dybgView).offset(11 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(32 * kWidthFactor, 20 * kWidthFactor));
  }];

  if (!_dytitleLabel) {
    _dytitleLabel = [[UILabel alloc] init];
  }
  _dytitleLabel.font = SourceHanSerifMediumFont(12 * kWidthFactor);
  _dytitleLabel.textColor = KColor_Black;
  _dytitleLabel.numberOfLines = 1;
  [self.dybgView addSubview:_dytitleLabel];
  [_dytitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.dybgView).offset(20 * kWidthFactor);
    make.left.equalTo(self.dybgView).offset(50 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(164 * kWidthFactor, 20 * kWidthFactor));
  }];

  if (!_dygrayLabel) {
    _dygrayLabel = [[UILabel alloc] init];
  }
  _dygrayLabel.layer.masksToBounds = YES;
  _dygrayLabel.layer.cornerRadius = 2 * kWidthFactor;
  _dygrayLabel.backgroundColor = KColor_HighBlack;
  [self.dybgView addSubview:_dygrayLabel];
  [_dygrayLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.dybgView).offset(13 * kWidthFactor);
    make.top.equalTo(self.dybgView).offset(40 * kWidthFactor);
    make.size.mas_equalTo(CGSizeMake(209 * kWidthFactor, 64 * kWidthFactor));
  }];

  if (!_dyfortitleLabel) {
    _dyfortitleLabel = [[UILabel alloc] init];
  }
  _dyfortitleLabel.font = SourceHanSerifRegularFont(12 * kWidthFactor);
  _dyfortitleLabel.textColor = KColor_White;
  _dyfortitleLabel.numberOfLines = 2;
  [self.dygrayLabel addSubview:_dyfortitleLabel];
  [_dyfortitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.dygrayLabel).offset(11 * kWidthFactor);
    make.centerY.equalTo(self.dygrayLabel);
    make.size.mas_equalTo(CGSizeMake(142 * kWidthFactor, 32 * kWidthFactor));
  }];

  if (!_dyimgView) {
    _dyimgView = [[UIImageView alloc] init];
  }
  //    _dyimgView.layer.masksToBounds = YES;
  //    _dyimgView.layer.cornerRadius = 3;
  [self.dybgView addSubview:_dyimgView];
  [_dyimgView mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(self.dygrayLabel.mas_right).offset(-8 * kWidthFactor);
    make.centerY.equalTo(self.dygrayLabel);
    make.size.mas_equalTo(CGSizeMake(40 * kWidthFactor, 40 * kWidthFactor));
  }];

  if (!_dyTagBtn) {
    _dyTagBtn = [UIButton buttonWithType:UIButtonTypeCustom];
  }
  [self.dybgView addSubview:_dyTagBtn];
  [_dyTagBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(self.dybgView);
    make.size.mas_equalTo(CGSizeMake(236 * kWidthFactor, 115 * kWidthFactor));
  }];

  if (!_audioCallTipsL) {
    _audioCallTipsL = [UILabel new];
    _audioCallTipsL.textColor = UIColor.darkGrayColor;
    _audioCallTipsL.font = SourceHanSerifRegularFont(11);
    _audioCallTipsL.textAlignment = NSTextAlignmentCenter;
  }
}

- (void)loadinfo:(IMUserInfoModelResult *)uinfo idf:(NSString *)idf {

  _iconImage.hidden = NO;
  if ([idf isEqualToString:CurrentUser.userid]) {
    [_iconImage
        sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:CurrentUser.avatar]
          placeholderImage:KImage_name(@"empty")];
    [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView.mas_right).offset(-26 * kWidthFactor);
      make.bottom.equalTo(self.contentView.mas_bottom)
          .offset(-10 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(20 * kWidthFactor, 20 * kWidthFactor));
    }];
  } else {
    [_iconImage
        sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:uinfo.avatar]
          placeholderImage:KImage_name(@"empty")];
    [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(24 * kWidthFactor);
      make.bottom.equalTo(self.contentView.mas_bottom)
          .offset(-10 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(20 * kWidthFactor, 20 * kWidthFactor));
    }];
  }
}

- (void)loadSoundTime:(int)sounTime idf:(NSString *)idf {
  _tempIdfer = idf;
  _dybgView.hidden = YES;
  [_photoView removeFromSuperview];
  [_titleDetailsLabel removeFromSuperview];
  [_audioCallTipsL removeFromSuperview];
  [self.contentView addSubview:_bgView];
  [_bgView addSubview:_soundTimeLabel];
  [_bgView addSubview:_soundImageView];
  [self.contentView addSubview:self.soundTagBtn];
  if ([idf isEqualToString:CurrentUser.userid]) {
    _bgView.backgroundColor = KColor_ImSoundBlack;
    [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView.mas_right).offset(-54 * kWidthFactor);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(66 * kWidthFactor, 20 * kWidthFactor));
    }];
    _soundTimeLabel.text = [NSString stringWithFormat:@"%ds", sounTime];
    [_soundTimeLabel setTextColor:KColor_White];
    _soundTimeLabel.textAlignment = NSTextAlignmentRight;
    [_soundTimeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.bgView.mas_right).offset(-7 * kWidthFactor);
      make.centerY.equalTo(self.bgView);
      make.size.mas_equalTo(CGSizeMake(30 * kWidthFactor, 15 * kWidthFactor));
    }];
    _soundImageView.image = KImage_name(@"播放");
    [_soundImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.bgView).offset(5 * kWidthFactor);
      make.centerY.equalTo(self.bgView);
      make.size.mas_equalTo(CGSizeMake(6 * kWidthFactor, 11 * kWidthFactor));
    }];
    [_soundTagBtn mas_makeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.contentView.mas_right).offset(-54 * kWidthFactor);
      make.top.equalTo(self.contentView).offset(12 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(66 * kWidthFactor, 16 * kWidthFactor));
    }];
  } else {
    _bgView.backgroundColor = KColor_White;
    [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(52 * kWidthFactor);
      make.centerY.equalTo(self.contentView);
      make.size.mas_equalTo(CGSizeMake(66 * kWidthFactor, 20 * kWidthFactor));
    }];
    _soundTimeLabel.text = [NSString stringWithFormat:@"%ds", sounTime];
    [_soundTimeLabel setTextColor:KColor_Black];
    _soundTimeLabel.textAlignment = NSTextAlignmentLeft;
    [_soundTimeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.bgView).offset(7 * kWidthFactor);
      make.centerY.equalTo(self.bgView);
      make.size.mas_equalTo(CGSizeMake(30 * kWidthFactor, 15 * kWidthFactor));
    }];
    _soundImageView.image = KImage_name(@"黑色播放");
    [_soundImageView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(self.bgView.mas_right).offset(-5 * kWidthFactor);
      make.centerY.equalTo(self.bgView);
      make.size.mas_equalTo(CGSizeMake(6 * kWidthFactor, 11 * kWidthFactor));
    }];
    [_soundTagBtn mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.contentView).offset(52 * kWidthFactor);
      make.top.equalTo(self.contentView).offset(12 * kWidthFactor);
      make.size.mas_equalTo(CGSizeMake(64 * kWidthFactor, 16 * kWidthFactor));
    }];
  }
}

- (void)loadAudioCallNotify:(NSString *)tips idf:(NSString *)idf {
  _tempIdfer = idf;
  _dybgView.hidden = YES;
  [_photoView removeFromSuperview];
  [_titleDetailsLabel removeFromSuperview];
  [_bgView removeFromSuperview];
  _iconImage.hidden = YES;

  _audioCallTipsL.text = tips;
  [self.contentView addSubview:_audioCallTipsL];
  [_audioCallTipsL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.center.equalTo(self.contentView);
  }];
}

- (void)loadDetailMsg:(NSString *)dmsg idf:(NSString *)idf {
  _tempIdfer = idf;
  _dybgView.hidden = YES;
  [_soundImageView removeFromSuperview];
  [_soundTimeLabel removeFromSuperview];
  [_audioCallTipsL removeFromSuperview];
  [self.soundTagBtn removeFromSuperview];
  if ([dmsg containsString:@"http://im.hz-52.com/"]) {
    NSString *strt = [dmsg substringToIndex:1];
    if ([strt isEqualToString:@"1"]) {
      [_titleDetailsLabel removeFromSuperview];
      [_bgView removeFromSuperview];
      [self.contentView addSubview:self.photoView];
      [_photoView getNum:11 index:1];
      NSArray *tempCountArr =
          [[dmsg substringFromIndex:2] componentsSeparatedByString:@","];
      _photoView.urlArray = tempCountArr;
      if ([idf isEqualToString:CurrentUser.userid]) {
        [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.right.equalTo(self.contentView.mas_right)
              .offset(-54 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(
              CGSizeMake(165 * kWidthFactor, 165 * kWidthFactor));
        }];
      } else {
        [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.contentView).offset(52 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(
              CGSizeMake(165 * kWidthFactor, 165 * kWidthFactor));
        }];
      }
    } else if ([strt isEqualToString:@"2"]) {
      [_titleDetailsLabel removeFromSuperview];
      [_bgView removeFromSuperview];
      [self.contentView addSubview:self.photoView];
      [_photoView getNum:12 index:1];
      NSArray *tempCountArr =
          [[dmsg substringFromIndex:2] componentsSeparatedByString:@","];
      _photoView.urlArray = tempCountArr;
      if ([idf isEqualToString:CurrentUser.userid]) {
        [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.right.equalTo(self.contentView.mas_right)
              .offset(-54 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(
              CGSizeMake(240 * kWidthFactor, 135 * kWidthFactor));
        }];
      } else {
        [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.contentView).offset(52 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(
              CGSizeMake(240 * kWidthFactor, 135 * kWidthFactor));
        }];
      }
    } else if ([strt isEqualToString:@"3"]) {
      [_titleDetailsLabel removeFromSuperview];
      [_bgView removeFromSuperview];
      [self.contentView addSubview:self.photoView];
      [_photoView getNum:13 index:1];
      NSArray *tempCountArr =
          [[dmsg substringFromIndex:2] componentsSeparatedByString:@","];
      _photoView.urlArray = tempCountArr;
      if ([idf isEqualToString:CurrentUser.userid]) {
        [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.right.equalTo(self.contentView.mas_right)
              .offset(-54 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(
              CGSizeMake(135 * kWidthFactor, 240 * kWidthFactor));
        }];
      } else {
        [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.contentView).offset(52 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(
              CGSizeMake(135 * kWidthFactor, 240 * kWidthFactor));
        }];
      }
    } else if ([strt isEqualToString:@"4"]) {
      _dybgView.hidden = NO;
      [_titleDetailsLabel removeFromSuperview];
      [_bgView removeFromSuperview];
      [_photoView removeFromSuperview];

      if ([idf isEqualToString:CurrentUser.userid]) {
        [_dybgView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.right.equalTo(self.contentView.mas_right)
              .offset(-54 * kWidthFactor);
          make.centerY.equalTo(self.contentView);
          make.size.mas_equalTo(
              CGSizeMake(236 * kWidthFactor, 115 * kWidthFactor));
        }];
      } else {
        [_dybgView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.contentView).offset(56 * kWidthFactor);
          make.centerY.equalTo(self.contentView);
          make.size.mas_equalTo(
              CGSizeMake(236 * kWidthFactor, 115 * kWidthFactor));
        }];
      }
      NSMutableArray *temppArray = [NemoUtil stringChangeArray:dmsg
                                                       typeStr:@"_"];
      _dytitleLabel.text = [temppArray objectAtIndex:2];
      _dyfortitleLabel.text = [temppArray objectAtIndex:3];
      CGSize tp = [NemoUtil
          calculateLabelHeightByText:SourceHanSerifMediumFont(12 * kWidthFactor)
                               width:156 * kWidthFactor
                           heightMax:43 * kWidthFactor
                             content:[temppArray objectAtIndex:2]];
      CGSize tpdown = [NemoUtil
          calculateLabelHeightByText:SourceHanSerifMediumFont(12 * kWidthFactor)
                               width:132 * kWidthFactor
                           heightMax:35 * kWidthFactor
                             content:[temppArray objectAtIndex:3]];
      [_dytitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dybgView).offset(8 * kWidthFactor);
        make.left.equalTo(self.dybgView).offset(50 * kWidthFactor);
        make.size.mas_equalTo(CGSizeMake(156 * kWidthFactor, tp.height));
      }];
      NSString *temimgStr = [temppArray objectAtIndex:4];
      if (temimgStr.length > 5) {
        _dyimgView.hidden = NO;
        [_dyimgView
            sd_setImageWithURL:[NemoUtil
                                   getUrlWithUserSmallIcon:
                                       [[NemoUtil stringChangeArray:temimgStr]
                                           objectAtIndex:0]]
              placeholderImage:KImage_name(@"empty")];
        [_dyfortitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.dygrayLabel).offset(8 * kWidthFactor);
          make.centerY.equalTo(self.dygrayLabel);
          make.size.mas_equalTo(
              CGSizeMake(132 * kWidthFactor, tpdown.height + 1));
        }];
      } else {
        _dyimgView.hidden = YES;
        [_dyfortitleLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.dygrayLabel).offset(8 * kWidthFactor);
          make.centerY.equalTo(self.dygrayLabel);
          make.size.mas_equalTo(
              CGSizeMake(132 * kWidthFactor, tpdown.height + 1));
        }];
      }
    } else {
      [_photoView removeFromSuperview];
      [self.contentView addSubview:_bgView];
      [self.contentView addSubview:_titleDetailsLabel];
      CGSize tp = [NemoUtil
          calculateLabelHeightByText:SourceHanSerifMediumFont(12 * kWidthFactor)
                               width:239 * kWidthFactor
                           heightMax:1000 * kWidthFactor
                             content:dmsg];
      _titleDetailsLabel.text = dmsg;
      [_titleDetailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(68 * kWidthFactor);
        make.top.equalTo(self.contentView).offset(21 * kWidthFactor);
        make.size.mas_equalTo(CGSizeMake(tp.width + 2, tp.height + 2));
      }];
      _bgView.backgroundColor = KColor_Random;

      if ([idf isEqualToString:CurrentUser.userid]) {
        _titleDetailsLabel.textColor = KColor_White;
        [_iconImage
            sd_setImageWithURL:[NemoUtil
                                   getUrlWithUserSmallIcon:CurrentUser.avatar]
              placeholderImage:KImage_name(@"empty")];
        [_titleDetailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.right.equalTo(self.contentView.mas_right)
              .offset(-68 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(21 * kWidthFactor);
          make.size.mas_equalTo(CGSizeMake(tp.width + 2, tp.height + 2));
        }];
        _bgView.backgroundColor = KColor_HighBlack;
        [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.right.equalTo(self.contentView.mas_right)
              .offset(-56 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(CGSizeMake(tp.width + 25 * kWidthFactor,
                                           tp.height + 20 * kWidthFactor));
        }];
      } else {
        _titleDetailsLabel.textColor = KColor_Black;
        _bgView.backgroundColor = KColor_White;
        [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.contentView).offset(56 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(5 * kWidthFactor);
          make.size.mas_equalTo(CGSizeMake(tp.width + 25 * kWidthFactor,
                                           tp.height + 20 * kWidthFactor));
        }];
        [_titleDetailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(self.contentView).offset(68 * kWidthFactor);
          make.top.equalTo(self.contentView).offset(10 * kWidthFactor);
          make.size.mas_equalTo(CGSizeMake(tp.width + 2, tp.height + 2));
        }];
      }
    }
    //
  } else {
    [_photoView removeFromSuperview];
    [self.contentView addSubview:_bgView];
    [self.contentView addSubview:_titleDetailsLabel];
    CGSize tp = [NemoUtil
        calculateLabelHeightByText:SourceHanSerifMediumFont(12 * kWidthFactor)
                             width:239 * kWidthFactor
                         heightMax:1000 * kWidthFactor
                           content:dmsg];
    _titleDetailsLabel.text = dmsg;
    _bgView.backgroundColor = KColor_Random;

    if ([idf isEqualToString:CurrentUser.userid]) {

      _bgView.backgroundColor = KColor_HighBlack;
      [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView.mas_right)
            .offset(-56 * kWidthFactor);
        make.top.equalTo(self.contentView).offset(6 * kWidthFactor);
        make.size.mas_equalTo(CGSizeMake(tp.width + 20 * kWidthFactor,
                                         tp.height + 2 + 6 * kWidthFactor));
      }];

      _titleDetailsLabel.textColor = KColor_White;
      [_iconImage
          sd_setImageWithURL:[NemoUtil
                                 getUrlWithUserSmallIcon:CurrentUser.avatar]
            placeholderImage:KImage_name(@"empty")];
      [_titleDetailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        //                make.right.equalTo(self.contentView.mas_right).offset(-66*kWidthFactor);
        make.right.equalTo(self.bgView.mas_right).offset(-10 * kWidthFactor);
        make.centerY.equalTo(self.bgView);
        //                make.top.equalTo(self.contentView).offset(6*kWidthFactor);
        make.size.mas_equalTo(CGSizeMake(tp.width + 2, tp.height + 2));
      }];
    } else {

      _bgView.backgroundColor = KColor_White;
      [_bgView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(56 * kWidthFactor);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(tp.width + 20 * kWidthFactor,
                                         tp.height + 2 + 5 * kWidthFactor));
      }];

      [_titleDetailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.bgView).offset(10 * kWidthFactor);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(tp.width + 2, tp.height + 2));
      }];
      _titleDetailsLabel.textColor = KColor_Black;
    }
  }
}

#pragma mark - Action
- (void)tapAction:(UITapGestureRecognizer *)sender {
  if (self.tapIconAction) {
    self.tapIconAction(self.tempIdfer);
  }
}

- (void)playingAudio:(BOOL)play {
  if (play) {
    _bgView.backgroundColor = KColor_heziGreen;
    _soundImageView.image = KImage_name(@"播放中");
  } else {
    _bgView.backgroundColor = KColor_ImSoundBlack;
    _soundImageView.image = KImage_name(@"播放");
  }
}

- (void)awakeFromNib {
  [super awakeFromNib];
  // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
  [super setSelected:selected animated:animated];

  // Configure the view for the selected state
}

@end
