#import "DynamicHeaderView.h"
#import "FTHZMusicPlayerView.h"
#import "HZPhotoGroupAbbreviation.h"

@interface DynamicHeaderView ()
@property(nonatomic, strong) UIImageView *iconbBgImage;
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *heziLabel;
@property(nonatomic, strong) UILabel *detailsLabel;
@property(nonatomic, strong) HZPhotoGroupAbbreviation *photoView;
@property(nonatomic, strong) UILabel *timeLabel;
@property(nonatomic, strong) FTHZMusicPlayerView *playerView;

@end

@implementation DynamicHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    UILongPressGestureRecognizer *longpress =
        [[UILongPressGestureRecognizer alloc]
            initWithTarget:self
                    action:@selector(longPressed:)];
    [self addGestureRecognizer:longpress];

    if (!_iconbBgImage) {
      _iconbBgImage = [[UIImageView alloc]
          initWithFrame:CGRectMake(29 * kMainTemp, 34 * kMainTemp,
                                   62 * kMainTemp, 62 * kMainTemp)];
    }
    _iconbBgImage.image = KImage_name(@"bowen");
    UIBezierPath *maskPath1 =
        [UIBezierPath bezierPathWithRoundedRect:_iconbBgImage.bounds
                              byRoundingCorners:UIRectCornerAllCorners
                                    cornerRadii:_iconbBgImage.bounds.size];
    CAShapeLayer *maskLayer1 = [[CAShapeLayer alloc] init];
    maskLayer1.frame = _iconbBgImage.bounds;
    maskLayer1.path = maskPath1.CGPath;
    _iconbBgImage.layer.mask = maskLayer1;
    [self addSubview:_iconbBgImage];
    [_iconbBgImage mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.centerX.equalTo(self.mas_left).offset(68 * kMainTemp);
      make.centerY.equalTo(self.mas_top).offset(31 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(62 * kMainTemp, 62 * kMainTemp));
    }];

    _iconbBgImage.userInteractionEnabled = YES;
    [_iconbBgImage
        addGestureRecognizer:[[UITapGestureRecognizer alloc]
                                 initWithTarget:self
                                         action:@selector(tapAction:)]];

    if (!_heziLabel) {
      _heziLabel = [[UILabel alloc] init];
    }
    [_heziLabel setFont:[UIFont fontWithName:@"DINCondensed-Bold"
                                        size:16 * kMainTemp]];
    [_heziLabel setTextColor:KColor_Black];
    [_heziLabel setLineBreakMode:NSLineBreakByWordWrapping];
    _heziLabel.textAlignment = NSTextAlignmentCenter;
    [self addSubview:self.heziLabel];
    [self.heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self).offset(12 * kMainTemp);
      make.top.equalTo(self).offset(25 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(27 * kMainTemp, 16 * kMainTemp));
    }];

    if (!_iconImage) {
      UIImageView *hzimage = [[UIImageView alloc] init];
      hzimage.image = [UIImage imageNamed:@"heziicon"];
      [self addSubview:hzimage];
      [hzimage mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.heziLabel.mas_right);
        make.bottom.equalTo(self.heziLabel.mas_top);
        make.size.mas_equalTo(CGSizeMake(8 * kMainTemp, 8 * kMainTemp));
      }];
      _iconImage = [[UIImageView alloc]
          initWithFrame:CGRectMake(16 * kMainTemp, 34 * kMainTemp,
                                   38 * kMainTemp, 38 * kMainTemp)];
    }
    UIBezierPath *maskPath =
        [UIBezierPath bezierPathWithRoundedRect:_iconImage.bounds
                              byRoundingCorners:UIRectCornerAllCorners
                                    cornerRadii:_iconImage.bounds.size];
    CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
    maskLayer.frame = _iconImage.bounds;
    maskLayer.path = maskPath.CGPath;
    _iconImage.layer.mask = maskLayer;
    [self addSubview:_iconImage];
    [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.heziLabel.mas_right).offset(10 * kMainTemp);
      make.top.equalTo(self).offset(12 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(38 * kMainTemp, 38 * kMainTemp));
    }];

    if (!_nameLabel) {
      _nameLabel = [[UILabel alloc] init];
    }
    [_nameLabel setTextColor:KColor_Black];
    [_nameLabel setLineBreakMode:NSLineBreakByWordWrapping];
    [_nameLabel setNumberOfLines:0];

    [self addSubview:self.nameLabel];
    [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
      make.top.equalTo(self).offset(12 * kMainTemp);
      make.width.equalTo(@(kMainWidth * 0.5));
      make.height.equalTo(@(16 * kMainTemp));
    }];

    if (!_awesomeBtn) {
      _awesomeBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
      _awesomeBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 12.5, 12.5, 12.5);
    }
    [self addSubview:_awesomeBtn];

    if (!_awesomeLabel) {
      _awesomeLabel = [[UILabel alloc] init];
    }
    [self addSubview:self.awesomeLabel];

    if (!_commentBtn) {
      _commentBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
      _commentBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 12.5, 12.5, 12.5);
    }
    [self addSubview:_commentBtn];

    if (!_vommentLabel) {
      _vommentLabel = [[UILabel alloc] init];
    }
    [self addSubview:self.vommentLabel];

    _playerView = [[FTHZMusicPlayerView alloc] init];
    _playerView.hidden = YES;
    [self addSubview:_playerView];
  }
  return self;
}

- (void)loadHeaderData:(AffairResult *)data {
  _dynamic = data;
  __weak typeof(self) wSelf = self;
  if (!_detailsLabel) {
    _detailsLabel = [[UILabel alloc] init];
  }
  CGSize tp = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kMainTemp]
                           width:252 * kMainTemp
                       heightMax:kMainHeight * 10
                         content:_dynamic.content];
  if ([_dynamic.content isEqualToString:@""]) {
    _detailsLabel.text = @"分享图片";
  } else {
    _detailsLabel.text = _dynamic.content;
  }
  [_detailsLabel setLineBreakMode:NSLineBreakByWordWrapping];
  [_detailsLabel setFont:[UIFont systemFontOfSize:14 * kMainTemp]];
  [_detailsLabel setTextColor:KColor_Gray];
  [_detailsLabel setNumberOfLines:0];
  [self addSubview:self.detailsLabel];
  [self.detailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    if ([NemoUtil getHZPhotoStringCount:wSelf.dynamic.images] > 0) {
      make.top.equalTo(self.nameLabel.mas_bottom).offset(8 * kMainTemp);
    } else {
      make.top.equalTo(self.nameLabel.mas_bottom).offset(12 * kMainTemp);
    }
    make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
    make.size.mas_lessThanOrEqualTo(CGSizeMake(252 * kMainTemp, tp.height + 2));
  }];
  UIView *upperView = self.detailsLabel;

  if ([NemoUtil getHZPhotoStringCount:_dynamic.images] > 0) {
    if (_photoView) {
      [_photoView removeFromSuperview];
      _photoView = nil;
    }
    if (!_photoView) {
      _photoView = [[HZPhotoGroupAbbreviation alloc] init];
    }
    [_photoView getNum:[NemoUtil getHZPhotoStringCount:_dynamic.images]
                 index:[self.dynamic.imageType integerValue]];
    NSArray *tempCountArr = [_dynamic.images componentsSeparatedByString:@","];
    _photoView.urlArray = tempCountArr;
    [self addSubview:self.photoView];
    [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
      make.top.equalTo(self.detailsLabel.mas_bottom).offset(12 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(
          256 * kMainTemp,
          [NemoUtil
              getHZPhotoHeight:[NemoUtil
                                   getHZPhotoStringCount:self.dynamic.images]
                         index:[self.dynamic.imageType integerValue]]));
    }];
    upperView = self.photoView;
  }
  {
    const BOOL hasMusic =
        [data.musicContent isKindOfClass:[MusicInfoData class]];
    self.playerView.hidden = !hasMusic;
    [self.playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
      make.right.equalTo(self.mas_right).offset(-24 * kMainTemp);
      make.top.equalTo(upperView.mas_bottom).offset(12 * kMainTemp);
      make.height.mas_equalTo(hasMusic ? 60 : 0);
    }];
    if (hasMusic) {
      [self.playerView setMusicInfo:data.musicContent
                               uuid:data.contentid
                          autoTrace:YES];
    }
    upperView = self.playerView;
  }

  if ([_dynamic.likeRs isEqualToString:@"0"]) {
    [_awesomeBtn setImage:KImage_name(@"Unlike") forState:UIControlStateNormal];
  } else {
    [_awesomeBtn setImage:KImage_name(@"like") forState:UIControlStateNormal];
  }
  [_awesomeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.nameLabel).offset(132 * kMainTemp);
    make.bottom.equalTo(self.mas_bottom).offset(-7.5 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 40 * kMainTemp));
  }];
  [_awesomeLabel setFont:[UIFont systemFontOfSize:12 * kMainTemp]];
  [_awesomeLabel setTextColor:KColor_Gray];
  [self.awesomeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.awesomeBtn.mas_right).offset(-7.5 * kMainTemp);
    make.centerY.equalTo(self.awesomeBtn);
    make.size.mas_equalTo(CGSizeMake(50 * kMainTemp, 12 * kMainTemp));
  }];

  [_commentBtn setImage:KImage_name(@"Comment") forState:UIControlStateNormal];
  [_commentBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.awesomeBtn.mas_right).offset(48 * kMainTemp);
    make.centerY.equalTo(self.awesomeBtn);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 40 * kMainTemp));
  }];

  [_vommentLabel setFont:[UIFont systemFontOfSize:12 * kMainTemp]];
  [_vommentLabel setTextColor:KColor_Gray];
  [self.vommentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.commentBtn.mas_right).offset(-7.5 * kMainTemp);
    make.centerY.equalTo(self.commentBtn);
    make.size.mas_equalTo(CGSizeMake(50 * kMainTemp, 12 * kMainTemp));
  }];

  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_dynamic.avatar]
        placeholderImage:KImage_name(@"empty")];
  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_dynamic.avatar]
        placeholderImage:KImage_name(@"empty")];
  _heziLabel.text = _dynamic.hertz;
  _awesomeLabel.text = _dynamic.likeNum;
  _vommentLabel.text = _dynamic.commentNum;

  UIFont *mediumFont = [UIFont systemFontOfSize:16 * kMainTemp]
                           ?: [UIFont systemFontOfSize:16 * kMainTemp];
  UIFont *regularFont = [UIFont systemFontOfSize:12 * kMainTemp]
                            ?: [UIFont systemFontOfSize:12 * kMainTemp];
  NSMutableAttributedString *userDescription =
      [[NSMutableAttributedString alloc]
          initWithString:_dynamic.nickName ?: @""
              attributes:@{
                NSFontAttributeName : mediumFont,
                NSForegroundColorAttributeName : KColor_Black,
              }];
  [userDescription
      appendAttributedString:
          [[NSAttributedString alloc]
              initWithString:[NSString
                                 stringWithFormat:@" %@", _dynamic.city ?: @""]
                  attributes:@{
                    NSFontAttributeName : regularFont,
                    NSForegroundColorAttributeName : KColor_Gray,
                  }]];
  _nameLabel.attributedText = [userDescription copy];

  if (!_timeLabel) {
    _timeLabel = [[UILabel alloc] init];
  }
  [_timeLabel setFont:[UIFont systemFontOfSize:12 * kMainTemp]];
  [_timeLabel setTextColor:KColor_Gray];
  _timeLabel.text =
      [NemoUtil distanceTimeWithBeforeTime:[_dynamic.createTime doubleValue]];
  [self addSubview:self.timeLabel];
  [self.timeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.nameLabel);
    make.bottom.equalTo(self.mas_bottom).offset(-20 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(140 * kMainTemp, 15 * kMainTemp));
  }];
}

#pragma mark - Action
- (void)tapAction:(UITapGestureRecognizer *)sender {
  if (self.tapIconAction) {
    self.tapIconAction();
  }
}

#pragma mark - Copy

- (void)longPressed:(UILongPressGestureRecognizer *)gesture {
  switch (gesture.state) {
  case UIGestureRecognizerStateBegan: {
    [self showPasteOption];
  } break;

  default:
    break;
  }
}

- (void)showPasteOption {
  [self layoutIfNeeded];
  FTHZPopMenuView *menu = [[FTHZPopMenuView alloc] init];
  @weakify(self);
  [menu setMenuItems:@[ [FTHZPopMenuItem
                         menuWithTitle:@"复制"
                                action:^{
                                  @strongify(self);
                                  [UIPasteboard generalPasteboard].string =
                                      self.detailsLabel.text ?: @"";
                                }] ]];
  [self becomeFirstResponder];
  [menu showInTargetRect:self.detailsLabel.frame
                  inView:self
          dismissHandler:^{
            @strongify(self);
            [self resignFirstResponder];
          }];
}

- (BOOL)canBecomeFirstResponder {
  return YES;
}

- (BOOL)canResignFirstResponder {
  return YES;
}

- (BOOL)becomeFirstResponder {
  self.detailsLabel.backgroundColor =
      [KColor_HighBlack colorWithAlphaComponent:0.1];
  return [super becomeFirstResponder];
}

- (BOOL)resignFirstResponder {
  self.detailsLabel.backgroundColor = [UIColor clearColor];
  return [super resignFirstResponder];
}

@end
