#import "AffairListModel.h"
#import "HZPhotoGroupAbbreviation.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface DynamicTableViewCell : UITableViewCell <HZPhotoGroupAbbreviationDelegate>
@property(nonatomic, strong) DynamicModelResult *dynamic;
@property(nonatomic, strong) FlatButton *awesomeBtn;
@property(nonatomic, strong) FlatButton *commentBtn;
@property(nonatomic, strong) FlatButton *tagBtn;

@property(nonatomic, copy) void (^tapIconAction)(NSString *uid)
    ; /**< 点击了用户头像*/

- (BOOL)isMusicPlaying;

@property(nonatomic, strong) UILongPressGestureRecognizer *longpress;

@end

NS_ASSUME_NONNULL_END
