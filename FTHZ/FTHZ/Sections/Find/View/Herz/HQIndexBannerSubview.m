#import "HQIndexBannerSubview.h"
#import "FTHZMusicPlayer.h"
#import "FTHZMusicPlayerView.h"
#import "HZPhotoGroupAbbreviation.h"

@interface HQIndexBannerSubview ()
@property(nonatomic, strong) FTHZMusicPlayerView *playerView;

@property(nonatomic, strong) FTHZHeziRes *model;
@end

@implementation HQIndexBannerSubview

- (void)setupData:(FTHZHeziRes *)model hasTop:(int)top {
  _model = model;
  @weakify(self);
#pragma mark -  背景框
  UIView *centerBgView = [[UIView alloc] init];
  centerBgView.layer.backgroundColor = [UIColor colorWithRed:255 / 255.0
                                                       green:255 / 255.0
                                                        blue:255 / 255.0
                                                       alpha:1.0]
                                           .CGColor;
  centerBgView.layer.cornerRadius = 8;
  centerBgView.layer.shadowColor = [UIColor colorWithRed:16 / 255.0
                                                   green:47 / 255.0
                                                    blue:95 / 255.0
                                                   alpha:0.08]
                                       .CGColor;
  centerBgView.layer.shadowOffset = CGSizeMake(0, 2.5);
  centerBgView.layer.shadowOpacity = 1;
  centerBgView.layer.shadowRadius = 7.5;

  [self.mainView addSubview:centerBgView];
  [centerBgView mas_makeConstraints:^(MASConstraintMaker *make) {
    @strongify(self);
    make.size.mas_equalTo(
        CGSizeMake(303 * kMainTemp, (top > 0 ? 511 : 481) * kMainTemp));
    make.top.equalTo(self.mainView).offset(top + 23.5 * kMainTemp);
    make.centerX.equalTo(self.mainView);
  }];

#pragma mark - 分享内容绘制
  UILabel *titleL = [UILabel new];
  titleL.textColor = KColor_textDarkGray;
  titleL.font = SourceHanSerifMediumFont(17);
  titleL.text = @"她曾分享：";
  if ([self.model.user.gender isEqualToString:@"1"]) {
    titleL.text = @"他曾分享：";
  }
  [centerBgView addSubview:titleL];
  [titleL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(centerBgView).offset(24 * kMainTemp);
    make.top.equalTo(centerBgView).offset(32 * kMainTemp);
  }];
  // 内容绘制
  int contentOffset = 0;
  if (self.model.content.images.length > 0) {
    // 图文类型
    NSArray *imgs =
        [self.model.content.images componentsSeparatedByString:@","];
    NSArray *imgList = imgs;
    if (imgs.count > 3) {
      imgList = [imgs subarrayWithRange:NSMakeRange(0, 3)];
    }

    HZPhotoGroupAbbreviation *photos = [[HZPhotoGroupAbbreviation alloc] init];
    [photos
        getNum:3
         index:2]; // 为了重用以前的组件，这里用了不太好的方式来设置他们的宽高
    photos.urlArray = imgList;

    [centerBgView addSubview:photos];
    [photos mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(centerBgView).offset(24 * kMainTemp);
      make.right.equalTo(centerBgView.mas_right).offset(-24 * kMainTemp);
      make.top.equalTo(titleL.mas_bottom).offset(20 * kMainTemp);
      make.height.mas_equalTo(80 * kMainTemp);
    }];
    contentOffset = (20 + 80) * kMainTemp;
  }

  MusicInfoData *musicData = self.model.content.musicInfo;
  const BOOL hasMusic = [musicData isKindOfClass:[MusicInfoData class]];

  if (hasMusic) {
    [centerBgView addSubview:self.playerView];
    [self.playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(centerBgView).offset(24 * kMainTemp);
      make.right.equalTo(centerBgView.mas_right).offset(-24 * kMainTemp);
      make.top.equalTo(titleL.mas_bottom)
          .offset(((contentOffset > 0 ? 10 : 25) + contentOffset) * kMainTemp);
      make.height.mas_equalTo(40 * kMainTemp);
    }];
    [self.playerView setMusicInfo:musicData
                             uuid:self.model.content.contentid
                        autoTrace:YES];

    contentOffset += (contentOffset > 0 ? 10 : 25) + 40 * kMainTemp;
  }

  UILabel *dyL = [UILabel new];

  if (contentOffset == 0) {
    // 两个引号
    UILabel *leftCom = [UILabel new];
    leftCom.text = @"“";
    leftCom.textColor = KColor_textGray;
    leftCom.font = SourceHanSerifSemiBoldFont(32 * kMainTemp);
    [centerBgView addSubview:leftCom];
    [leftCom mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(centerBgView).offset(24 * kMainTemp);
      make.top.equalTo(titleL.mas_bottom)
          .offset((top > 0 ? 72 : 60) * kMainTemp);
    }];
    dyL.text = self.model.content.content;
    dyL.textColor = KColor_textGray;
    dyL.font = SourceHanSerifRegularFont(14 * kMainTemp);
    dyL.numberOfLines = 3;

    [centerBgView addSubview:dyL];
    [dyL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(centerBgView).offset(60 * kMainTemp);
      make.right.equalTo(centerBgView.mas_right).offset(-60 * kMainTemp);
      make.top.equalTo(leftCom.mas_bottom).offset(-20 * kMainTemp);
    }];

    UILabel *rightCom = [UILabel new];
    rightCom.text = @"”";
    rightCom.textColor = KColor_textGray;
    rightCom.font = SourceHanSerifSemiBoldFont(32 * kMainTemp);
    [centerBgView addSubview:rightCom];
    [rightCom mas_makeConstraints:^(MASConstraintMaker *make) {
      make.right.equalTo(centerBgView).offset(-24 * kMainTemp);
      make.top.equalTo(dyL.mas_bottom).offset(-10 * kMainTemp);
    }];
  } else {
    // 混合内容的动态
    dyL.text = self.model.content.content;
    dyL.textColor = KColor_textGray;
    dyL.font = SourceHanSerifRegularFont(14 * kMainTemp);
    dyL.numberOfLines = 2;

    [centerBgView addSubview:dyL];
    [dyL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(centerBgView).offset(24 * kMainTemp);
      make.right.equalTo(centerBgView.mas_right).offset(-24 * kMainTemp);
      make.top.equalTo(titleL.mas_bottom)
          .offset((contentOffset + 10) * kMainTemp);
    }];
  }

  UIButton *contentMoreBt = [UIButton new];
  [contentMoreBt setTitle:@"查看更多" forState:UIControlStateNormal];
  [contentMoreBt setTitleColor:KColor_heziGreen forState:UIControlStateNormal];
  contentMoreBt.titleLabel.font = SourceHanSerifMediumFont(14 * kMainTemp);
  [contentMoreBt addTarget:self
                    action:@selector(contentAction)
          forControlEvents:UIControlEventTouchUpInside];
  [centerBgView addSubview:contentMoreBt];
  int offset = 45;
  if (contentOffset > 100 * kMainTemp) {
    offset = 10;
  } else if (contentOffset == 100 * kMainTemp) {
    offset = 25;
  }
  [contentMoreBt mas_makeConstraints:^(MASConstraintMaker *make) {
    make.right.equalTo(centerBgView.mas_right).offset(-24 * kMainTemp);
    make.top.equalTo(dyL.mas_bottom).offset(offset * kMainTemp);
  }];

#pragma mark -用户信息部分
  UIImageView *userIv = [[UIImageView alloc] init];
  [userIv sd_setImageWithURL:[NSURL URLWithString:self.model.user.avatar]];
  userIv.layer.cornerRadius = 16 * kMainTemp;
  userIv.layer.masksToBounds = YES;
  [centerBgView addSubview:userIv];
  [userIv mas_makeConstraints:^(MASConstraintMaker *make) {
    make.size.mas_equalTo(CGSizeMake(32 * kMainTemp, 32 * kMainTemp));
    make.left.equalTo(centerBgView).offset(24 * kMainTemp);
    make.top.equalTo(contentMoreBt.mas_bottom)
        .offset((contentOffset > 100 * kMainTemp ? 22 : 48) * kMainTemp);
  }];

  UILabel *userNameL = [UILabel new];
  userNameL.text = self.model.user.nickname;
  userNameL.textColor = KColor_textGray;
  userNameL.font = SourceHanSerifRegularFont(14 * kMainTemp);
  [centerBgView addSubview:userNameL];
  [userNameL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(userIv.mas_right).offset(8 * kMainTemp);
    make.top.equalTo(userIv).offset(-2 * kMainTemp);
  }];

  UILabel *herzL = [UILabel new];
  herzL.text = self.model.user.hertz;
  herzL.textColor = KColor_textDarkGray;
  herzL.font = DinCondensedBoldFont(15 * kMainTemp);
  [centerBgView addSubview:herzL];
  [herzL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.centerY.equalTo(userNameL);
    make.left.equalTo(userNameL.mas_right).offset(5 * kMainTemp);
  }];
  UILabel *heziIcon = [UILabel new];
  heziIcon.textColor = KColor_heziGreen;
  heziIcon.font = DinCondensedBoldFont(7.5 * kMainTemp);
  heziIcon.text = @"HZ";
  [centerBgView addSubview:heziIcon];
  [heziIcon mas_makeConstraints:^(MASConstraintMaker *make) {
    make.bottom.equalTo(herzL.mas_top);
    make.left.equalTo(herzL.mas_right);
  }];

  UILabel *signL = [UILabel new];
  signL.text = self.model.user.signature;
  signL.textColor = KColor_textDarkGray;
  signL.font = SourceHanSerifRegularFont(10 * kMainTemp);
  [centerBgView addSubview:signL];
  [signL mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(userNameL);
    make.bottom.equalTo(userIv).offset(2 * kMainTemp);
  }];

  UIView *tempView = nil;
  for (int i = 0; i < self.model.user.tag.count; i++) {
    TagModelResult *tagInfo =
        [TagModelResult mj_objectWithKeyValues:self.model.user.tag[i]];
    UIView *tagBgView = [UIView new];
    tagBgView.backgroundColor = KColor_HighBlack;
    tagBgView.layer.cornerRadius = 4.0;
    tagBgView.layer.masksToBounds = YES;

    UILabel *tagL = [UILabel new];
    tagL.text = tagInfo.name;
    tagL.textColor = UIColor.whiteColor;
    tagL.font = SourceHanSerifRegularFont(10 * kMainTemp);
    [tagBgView addSubview:tagL];
    [tagL mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(tagBgView).offset(6 * kMainTemp);
      make.right.equalTo(tagBgView).offset(-6 * kMainTemp);
      make.top.equalTo(tagBgView).offset(4 * kMainTemp);
      make.bottom.equalTo(tagBgView).offset(-4 * kMainTemp);
    }];

    [centerBgView addSubview:tagBgView];
    if (tempView == nil) {
      [tagBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(userNameL);
        make.top.equalTo(signL.mas_bottom).offset(12 * kMainTemp);
      }];
    } else {
      if (i % 2 > 0) {
        // 同一排
        [tagBgView mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.mas_equalTo(tempView.mas_right).offset(12 * kMainTemp);
          make.centerY.equalTo(tempView);
        }];
      } else {
        // 下一排
        [tagBgView mas_makeConstraints:^(MASConstraintMaker *make) {
          make.left.equalTo(userNameL);
          make.top.mas_equalTo(tempView.mas_bottom).offset(10 * kMainTemp);
        }];
      }
    }
    tempView = tagBgView;
  }
}

- (FTHZMusicPlayerView *)playerView {
  if (!_playerView) {
    _playerView = [[FTHZMusicPlayerView alloc] init];
  }
  return _playerView;
}
#pragma mark -action
- (void)contentAction {
  DynamicModelResult *dy = [[DynamicModelResult alloc] init];
  dy.affair = [AffairResult new];
  dy.affair.aid = self.model.content.contentid;
  dy.affair.content = self.model.content.content;
  dy.affair.images = self.model.content.images;
  dy.affair.musicContent = self.model.content.musicInfo;

  dy.user = [UserResult new];
  dy.user.uid = self.model.user.userid;
  dy.user.avatar = self.model.user.avatar;
  dy.user.hertz = self.model.user.hertz;

  [self.delegate contentMore:dy];
}

- (void)userAction {
  [self.delegate userMore:self.model.user.userid];
}

#pragma mark -lifecyle
- (instancetype)initWithFrame:(CGRect)frame {

  self = [super initWithFrame:frame];

  if (self) {

    [self addSubview:self.mainView];
    [self addSubview:self.coverView];
    UITapGestureRecognizer *singleTap = [[UITapGestureRecognizer alloc]
        initWithTarget:self
                action:@selector(singleCellTapAction:)];
    [self addGestureRecognizer:singleTap];
  }

  return self;
}

- (void)singleCellTapAction:(UIGestureRecognizer *)gesture {
  if (self.didSelectCellBlock) {
    self.didSelectCellBlock(self.tag, self);
  }
}

- (void)setSubviewsWithSuperViewBounds:(CGRect)superViewBounds {
  if (CGRectEqualToRect(self.mainView.frame, superViewBounds)) {
    return;
  }

  self.mainView.frame = superViewBounds;
  self.coverView.frame = superViewBounds;
}

- (UIView *)mainView {

  if (!_mainView) {
    _mainView = [[UIView alloc] init];
    _mainView.userInteractionEnabled = YES;
  }
  return _mainView;
}

- (UIImageView *)iconImage {

  if (!_iconImage) {
    _iconImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(0, 0, self.frame.size.width,
                                 self.frame.size.height)];
    _iconImage.contentMode = UIViewContentModeScaleAspectFit;
  }
  return _iconImage;
}

- (UIView *)coverView {

  if (!_coverView) {
    _coverView = [[UIView alloc] init];
  }
  return _coverView;
}

@end
