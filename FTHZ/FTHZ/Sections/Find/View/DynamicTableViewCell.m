#import "DynamicTableViewCell.h"
#import "FTHZMusicPlayer.h"
#import "FTHZMusicPlayerView.h"
#import "FTHZSimpleVideoPlayer.h"
#import "HZPhotoGroupAbbreviation.h"
#import <AVFoundation/AVFoundation.h>
#import <AVKit/AVKit.h>

@interface DynamicTableViewCell ()
@property(nonatomic, strong) UIImageView *iconbBgImage;
@property(nonatomic, strong) UIImageView *iconImage;
@property(nonatomic, strong) UILabel *nameLabel;
@property(nonatomic, strong) UILabel *heziLabel;
@property(nonatomic, strong) UILabel *detailsLabel;
@property(nonatomic, strong) HZPhotoGroupAbbreviation *photoView;
@property(nonatomic, strong) UILabel *awesomeLabel;
@property(nonatomic, strong) UILabel *vommentLabel;
@property(nonatomic, strong) UILabel *lineLabel;
@property(nonatomic, strong) FTHZMusicPlayerView *playerView;
@property(nonatomic, strong) FTHZSimpleVideoPlayer *videoPlayerView;

@end

@implementation DynamicTableViewCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style
              reuseIdentifier:(NSString *)reuseIdentifier {
  self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
  if (self) {
    [self loadCellView];
    [self setSelectionStyle:(UITableViewCellSelectionStyleNone)];
  }
  return self;
}

- (void)longPressed:(UILongPressGestureRecognizer *)gesture {
  switch (gesture.state) {
  case UIGestureRecognizerStateBegan: {
    [self showPasteOption];
  } break;

  default:
    break;
  }
}

- (void)showPasteOption {
  [self layoutIfNeeded];
  FTHZPopMenuView *menu = [[FTHZPopMenuView alloc] init];
  @weakify(self);
  [menu setMenuItems:@[ [FTHZPopMenuItem
                         menuWithTitle:@"复制"
                                action:^{
                                  @strongify(self);
                                  [UIPasteboard generalPasteboard].string =
                                      self.detailsLabel.text ?: @"";
                                }] ]];
  [menu showInTargetRect:self.detailsLabel.frame
                  inView:self.contentView
          dismissHandler:^{

          }];
}

- (void)loadCellView {
  if (!_iconbBgImage) {
    _iconbBgImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(29 * kMainTemp, 34 * kMainTemp, 62 * kMainTemp,
                                 62 * kMainTemp)];
  }
  _iconbBgImage.image = KImage_name(@"bowen");
  UIBezierPath *maskPath1 =
      [UIBezierPath bezierPathWithRoundedRect:_iconbBgImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconbBgImage.bounds.size];
  CAShapeLayer *maskLayer1 = [[CAShapeLayer alloc] init];
  maskLayer1.frame = _iconbBgImage.bounds;
  maskLayer1.path = maskPath1.CGPath;
  _iconbBgImage.layer.mask = maskLayer1;
  [self.contentView addSubview:_iconbBgImage];
  [_iconbBgImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(37 * kMainTemp);
    make.top.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(62 * kMainTemp, 62 * kMainTemp));
  }];

  _iconbBgImage.userInteractionEnabled = YES;
  [_iconbBgImage
      addGestureRecognizer:[[UITapGestureRecognizer alloc]
                               initWithTarget:self
                                       action:@selector(tapAction:)]];

  if (!_heziLabel) {
    _heziLabel = [[UILabel alloc] init];
  }
  [_heziLabel setFont:[UIFont fontWithName:@"DINCondensed-Bold"
                                      size:16 * kMainTemp]];
  [_heziLabel setTextColor:KColor_Black];
  [_heziLabel setLineBreakMode:NSLineBreakByWordWrapping];
  _heziLabel.textAlignment = NSTextAlignmentCenter;
  [self.contentView addSubview:self.heziLabel];
  [self.heziLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.contentView).offset(12 * kMainTemp);
    make.top.equalTo(self.contentView).offset(20 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(27 * kMainTemp, 16 * kMainTemp));
  }];

  if (!_iconImage) {
    UIImageView *hzimage = [[UIImageView alloc] init];
    hzimage.image = [UIImage imageNamed:@"heziicon"];
    [self.contentView addSubview:hzimage];
    [hzimage mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.heziLabel.mas_right);
      make.bottom.equalTo(self.heziLabel.mas_top);
      make.size.mas_equalTo(CGSizeMake(8 * kMainTemp, 8 * kMainTemp));
    }];
    _iconImage = [[UIImageView alloc]
        initWithFrame:CGRectMake(16 * kMainTemp, 34 * kMainTemp, 38 * kMainTemp,
                                 38 * kMainTemp)];
  }

  UIBezierPath *maskPath =
      [UIBezierPath bezierPathWithRoundedRect:_iconImage.bounds
                            byRoundingCorners:UIRectCornerAllCorners
                                  cornerRadii:_iconImage.bounds.size];
  CAShapeLayer *maskLayer = [[CAShapeLayer alloc] init];
  maskLayer.frame = _iconImage.bounds;
  maskLayer.path = maskPath.CGPath;
  _iconImage.layer.mask = maskLayer;
  [self.contentView addSubview:_iconImage];
  [_iconImage mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.heziLabel.mas_right).offset(10 * kMainTemp);
    make.top.equalTo(self.contentView).offset(12 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(38 * kMainTemp, 38 * kMainTemp));
  }];

  if (!_nameLabel) {
    _nameLabel = [[UILabel alloc] init];
  }
  [_nameLabel setTextColor:KColor_Black];
  [_nameLabel setNumberOfLines:0];
  [self.contentView addSubview:self.nameLabel];
  [self.nameLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
    make.top.equalTo(self.contentView).offset(12 * kMainTemp);
    make.width.equalTo(@(kMainWidth * 0.9));
    make.height.equalTo(@(16 * kMainTemp));
  }];

  if (!_detailsLabel) {
    _detailsLabel = [[UILabel alloc] init];
  }
  [_detailsLabel setFont:[UIFont systemFontOfSize:14 * kMainTemp]];
  [_detailsLabel setTextColor:KColor_Gray];
  [_detailsLabel setNumberOfLines:0];
  [self.contentView addSubview:self.detailsLabel];

  if (!_awesomeBtn) {
    _awesomeBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  }
  [self.contentView addSubview:_awesomeBtn];
  _awesomeBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 12.5, 12.5, 12.5);
  [_awesomeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.nameLabel).offset(140 * kMainTemp);
    make.bottom.equalTo(self.contentView.mas_bottom).offset(-7.5 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 40 * kMainTemp));
  }];
  if (!_awesomeLabel) {
    _awesomeLabel = [[UILabel alloc] init];
  }
  [_awesomeLabel setFont:[UIFont systemFontOfSize:12 * kMainTemp]];
  [_awesomeLabel setTextColor:KColor_Gray];
  [self.contentView addSubview:self.awesomeLabel];
  [self.awesomeLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.awesomeBtn.mas_right).offset(-7.5 * kMainTemp);
    make.centerY.equalTo(self.awesomeBtn);
    make.size.mas_equalTo(CGSizeMake(50 * kMainTemp, 12 * kMainTemp));
  }];

  if (!_commentBtn) {
    _commentBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  }
  [_commentBtn setImage:KImage_name(@"Comment") forState:UIControlStateNormal];
  _commentBtn.imageEdgeInsets = UIEdgeInsetsMake(12.5, 12.5, 12.5, 12.5);
  [self.contentView addSubview:_commentBtn];
  [_commentBtn mas_makeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.awesomeBtn.mas_right).offset(35 * kMainTemp);
    make.centerY.equalTo(self.awesomeBtn);
    make.size.mas_equalTo(CGSizeMake(40 * kMainTemp, 40 * kMainTemp));
  }];
  if (!_vommentLabel) {
    _vommentLabel = [[UILabel alloc] init];
  }
  [_vommentLabel setFont:[UIFont systemFontOfSize:12 * kMainTemp]];
  [_vommentLabel setTextColor:KColor_Gray];

  [self.contentView addSubview:self.vommentLabel];
  [self.vommentLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.left.equalTo(self.commentBtn.mas_right).offset(-7.5 * kMainTemp);
    make.centerY.equalTo(self.commentBtn);
    make.size.mas_equalTo(CGSizeMake(50 * kMainTemp, 12 * kMainTemp));
  }];

  if (!_lineLabel) {
    _lineLabel = [[UILabel alloc] init];
  }
  _lineLabel.backgroundColor = KColor_LineGray;
  [self.contentView addSubview:self.lineLabel];
  [self.lineLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    make.top.equalTo(self.iconImage.mas_bottom).offset(12 * kMainTemp);
    make.centerX.equalTo(self.iconImage);
    make.bottom.equalTo(self.contentView);
    make.size.mas_equalTo(CGSizeMake(2 * kMainTemp, 500 * kMainTemp));
  }];

  if (!_tagBtn) {
    _tagBtn = [FlatButton buttonWithType:UIButtonTypeCustom];
  }
  _tagBtn.backgroundColor = KColor_Black;
  _tagBtn.titleLabel.font = [UIFont systemFontOfSize:10 * kMainTemp];
  _tagBtn.titleLabel.textColor = KColor_White;
  _tagBtn.layer.masksToBounds = YES;
  _tagBtn.layer.cornerRadius = 3;
  [self.contentView addSubview:_tagBtn];

  if (!_photoView) {
    _photoView = [[HZPhotoGroupAbbreviation alloc] init];
  }
  [self.contentView addSubview:self.playerView];
  self.playerView.hidden = YES;
}

- (void)setDynamic:(DynamicModelResult *)dynamic {
  _dynamic = dynamic;
  __weak typeof(self) wSelf = self;

  CGSize tp = [NemoUtil
      calculateLabelHeightByText:[UIFont systemFontOfSize:14 * kMainTemp]
                           width:252 * kMainTemp
                       heightMax:100 * kMainTemp
                         content:_dynamic.affair.content];
  if ([_dynamic.affair.content isEqualToString:@""]) {
    _detailsLabel.text = @"分享图片";
  } else {
    _detailsLabel.text = _dynamic.affair.content;
  }
  [self.detailsLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
    if ([NemoUtil getHZPhotoStringCount:wSelf.dynamic.affair.images] > 0) {
      make.top.equalTo(self.nameLabel.mas_bottom).offset(8 * kMainTemp);
    } else {
      make.top.equalTo(self.nameLabel.mas_bottom).offset(12 * kMainTemp);
    }
    make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
    make.size.mas_equalTo(CGSizeMake(252 * kMainTemp, tp.height + 10));
  }];

  UIView *upperView = self.detailsLabel;

  if ([NemoUtil getHZPhotoStringCount:_dynamic.affair.images] > 0) {
    [self.contentView addSubview:self.photoView];

    _photoView.isVideo = NO;
    _photoView.videoURL = nil;
    _photoView.delegate = self;

    BOOL isVideo = [_dynamic.affair.type isEqualToString:@"4"];
    _photoView.isVideo = isVideo;
    if (isVideo) {
      _photoView.videoURL = _dynamic.affair.video;
    }

    [_photoView getNum:[NemoUtil getHZPhotoStringCount:_dynamic.affair.images]
                 index:[_dynamic.affair.imageType integerValue]];
    NSArray *tempCountArr =
        [_dynamic.affair.images componentsSeparatedByString:@","];
    _photoView.urlArray = tempCountArr;

    [self.photoView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
      make.top.equalTo(self.detailsLabel.mas_bottom).offset(12 * kMainTemp);
      make.size.mas_equalTo(CGSizeMake(
          256 * kMainTemp,
          [NemoUtil
              getHZPhotoHeight:[NemoUtil
                                   getHZPhotoStringCount:self.dynamic.affair
                                                             .images]
                         index:[self.dynamic.affair.imageType integerValue]]));
    }];
    upperView = self.photoView;
  } else {
    [_photoView removeFromSuperview];
  }

  {
    MusicInfoData *musicData = dynamic.affair.musicContent;
    const BOOL hasMusic = [musicData isKindOfClass:[MusicInfoData class]];
    [self.playerView mas_remakeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.iconImage.mas_right).offset(8 * kMainTemp);
      make.right.equalTo(self.contentView.mas_right).offset(-24 * kMainTemp);
      make.top.equalTo(upperView.mas_bottom).offset(12 * kMainTemp);
      make.height.mas_equalTo(hasMusic ? 60 : 0);
    }];
    self.playerView.hidden = !hasMusic;
    if (hasMusic) {
      [self.playerView setMusicInfo:musicData
                               uuid:dynamic.affair.aid
                          autoTrace:YES];
    }
  }

  [_iconImage
      sd_setImageWithURL:[NemoUtil getUrlWithUserSmallIcon:_dynamic.user.avatar]
        placeholderImage:KImage_name(@"empty")];
  _heziLabel.text = _dynamic.user.hertz;
  _awesomeLabel.text = _dynamic.affair.likeNum;
  _vommentLabel.text = _dynamic.affair.commentNum;

  UIFont *mediumFont = [UIFont systemFontOfSize:16 * kMainTemp]
                           ?: [UIFont systemFontOfSize:16 * kMainTemp];
  UIFont *regularFont = [UIFont systemFontOfSize:12 * kMainTemp]
                            ?: [UIFont systemFontOfSize:12 * kMainTemp];
  NSMutableAttributedString *userDescription =
      [[NSMutableAttributedString alloc]
          initWithString:_dynamic.user.nickname ?: @""
              attributes:@{
                NSFontAttributeName : mediumFont,
                NSForegroundColorAttributeName : KColor_Black,
              }];
  [userDescription
      appendAttributedString:
          [[NSAttributedString alloc]
              initWithString:[NSString
                                 stringWithFormat:@" %@",
                                                  _dynamic.user.city ?: @""]
                  attributes:@{
                    NSFontAttributeName : regularFont,
                    NSForegroundColorAttributeName : KColor_Gray,
                  }]];
  _nameLabel.attributedText = [userDescription copy];

  if ([_dynamic.affair.likeRs isEqualToString:@"0"]) {
    [_awesomeBtn setImage:KImage_name(@"Unlike") forState:UIControlStateNormal];
  } else {
    [_awesomeBtn setImage:KImage_name(@"like") forState:UIControlStateNormal];
  }

  if (_dynamic.affair.tagName.length > 0) {
    self.tagBtn.hidden = NO;
    CGSize tagNamel = [NemoUtil
        calculateLabelHeightByText:[UIFont systemFontOfSize:10 * kMainTemp]
                             width:252 * kMainTemp
                         heightMax:100 * kMainTemp
                           content:_dynamic.affair.tagName];
    [_tagBtn setTitle:_dynamic.affair.tagName forState:UIControlStateNormal];
    [_tagBtn mas_makeConstraints:^(MASConstraintMaker *make) {
      make.left.equalTo(self.nameLabel);
      make.centerY.equalTo(self.commentBtn);
      make.size.mas_equalTo(CGSizeMake(75 * kMainTemp, 18 * kMainTemp));
    }];
  } else {
    self.tagBtn.hidden = YES;
  }
}

#pragma mark - HZPhotoGroupAbbreviationDelegate
- (void)photoGroup:(HZPhotoGroupAbbreviation *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL {
  [[FTHZSimpleVideoPlayer sharedInstance] playVideoWithURL:videoURL
                                                    inView:photoGroup];
}

- (void)prepareForReuse {
  [super prepareForReuse];
  [[FTHZSimpleVideoPlayer sharedInstance] stop];
  _photoView.isVideo = NO;
  _photoView.videoURL = nil;
  [_photoView removeFromSuperview];
}

- (FTHZMusicPlayerView *)playerView {
  if (!_playerView) {
    _playerView = [[FTHZMusicPlayerView alloc] init];
  }
  return _playerView;
}

- (BOOL)isMusicPlaying {
  return [self.playerView isPlayerLoadedCurrentMusic];
}

#pragma mark - Action
- (void)tapAction:(UITapGestureRecognizer *)sender {
  if (self.tapIconAction) {
    self.tapIconAction(self.dynamic.user.uid);
  }
}

@end
