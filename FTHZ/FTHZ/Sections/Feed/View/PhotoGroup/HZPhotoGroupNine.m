#import "HZPhotoGroupNine.h"
#import "FLAnimatedImageView.h"
#import "HZPhotoBrowser.h"
#import "HZPhotoBrowserConfig.h"
#import "UIButton+WebCache.h"
#import <AVKit/AVKit.h>
#import <FLAnimatedImage/FLAnimatedImageView.h>

#define HZPhotoGroupImageMargin 8 * kWidthFactor

@interface HZPhotoGroupNine () <HZPhotoBrowserDelegate>
@property(nonatomic, assign) CGRect *tempRect;
@property(nonatomic, assign) NSInteger imageNum;
@property(nonatomic, assign) NSInteger imageIndex;

@end

@implementation HZPhotoGroupNine

- (instancetype)init {
  self = [super init];
  if (self) {
  }
  return self;
}

- (void)getNum:(NSInteger)num index:(NSInteger)index {
  _imageNum = num;
  _imageIndex = index;
}

- (void)setUrlArray:(NSArray<NSString *> *)urlArray {
  _urlArray = urlArray;
  [self.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];

  [urlArray
      enumerateObjectsUsingBlock:^(NSString *obj, NSUInteger idx, BOOL *stop) {
        FLAnimatedImageView *imageView = [[FLAnimatedImageView alloc] init];
        imageView.userInteractionEnabled = YES;
        imageView.contentMode = UIViewContentModeScaleAspectFill;
        imageView.clipsToBounds = YES;
        imageView.layer.borderColor = [[UIColor colorWithWhite:0.85
                                                         alpha:1.0] CGColor];
        imageView.layer.borderWidth = 0.5;

        [imageView sd_setImageWithURL:[NemoUtil getUrlWithUserPictaure:obj]
                     placeholderImage:KImage_name(@"empty")];

        imageView.tag = idx;
        UITapGestureRecognizer *tap =
            [[UITapGestureRecognizer alloc] initWithTarget:self
                                                    action:@selector(tap:)];
        [imageView addGestureRecognizer:tap];

        [self addSubview:imageView];

        if (idx == 0 && self.isVideo) {
          UIView *playButtonBg = [[UIView alloc] init];
          playButtonBg.backgroundColor =
              [[UIColor blackColor] colorWithAlphaComponent:0.2];
          playButtonBg.layer.cornerRadius = 10 * kWidthFactor;
          [imageView addSubview:playButtonBg];

          [playButtonBg mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(imageView).offset(-8 * kWidthFactor);
            make.bottom.equalTo(imageView).offset(-8 * kWidthFactor);
            make.width.height.mas_equalTo(20 * kWidthFactor);
          }];

          CAShapeLayer *circleLayer = [CAShapeLayer layer];
          UIBezierPath *circlePath = [UIBezierPath
              bezierPathWithArcCenter:CGPointMake(10 * kWidthFactor,
                                                  10 * kWidthFactor)
                               radius:8 * kWidthFactor
                           startAngle:0
                             endAngle:2 * M_PI
                            clockwise:YES];
          circleLayer.path = circlePath.CGPath;
          circleLayer.strokeColor =
              [[UIColor whiteColor] colorWithAlphaComponent:0.8].CGColor;
          circleLayer.fillColor = [UIColor clearColor].CGColor;
          circleLayer.lineWidth = 1.5;
          [playButtonBg.layer addSublayer:circleLayer];

          CAShapeLayer *triangleLayer = [CAShapeLayer layer];
          UIBezierPath *trianglePath = [UIBezierPath bezierPath];
          [trianglePath
              moveToPoint:CGPointMake(8 * kWidthFactor, 6 * kWidthFactor)];
          [trianglePath
              addLineToPoint:CGPointMake(8 * kWidthFactor, 14 * kWidthFactor)];
          [trianglePath
              addLineToPoint:CGPointMake(14 * kWidthFactor, 10 * kWidthFactor)];
          [trianglePath closePath];
          triangleLayer.path = trianglePath.CGPath;
          triangleLayer.fillColor =
              [[UIColor whiteColor] colorWithAlphaComponent:0.8].CGColor;
          [playButtonBg.layer addSublayer:triangleLayer];
        }
      }];
}

- (void)layoutSubviews {
  [super layoutSubviews];
  long imageCount = self.urlArray.count;
  int perRowImageCount = ((imageCount == 4) ? 2 : 3);
  CGFloat perRowImageCountF = (CGFloat)perRowImageCount;
  int totalRowCount = ceil(imageCount / perRowImageCountF);
  if (_imageNum == 1) {
    if (_imageIndex == 1) {
      CGFloat w = 365 * kWidthFactor;
      CGFloat h = 228 * kWidthFactor;
      [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                  BOOL *stop) {
        btn.layer.cornerRadius = 12 * kMainTemp;
        btn.frame = CGRectMake(0, 0, w, h);
      }];
    } else if (_imageIndex == 2) {
      CGFloat w = 280 * kWidthFactor;
      CGFloat h = 280 * kWidthFactor;
      [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                  BOOL *stop) {
        btn.layer.cornerRadius = 12 * kMainTemp;
        btn.frame = CGRectMake(0, 0, w, h);
      }];
    } else {
      CGFloat w = 228 * kWidthFactor;
      CGFloat h = 365 * kWidthFactor;
      [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                  BOOL *stop) {
        btn.layer.cornerRadius = 12 * kMainTemp;
        btn.frame = CGRectMake(0, 0, w, h);
      }];
    }
  }
  if (_imageNum == 2) {
    CGFloat w = 174 * kWidthFactor;
    CGFloat h = 174 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + 2 * HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, 0, w, h);
    }];
  }
  if (_imageNum == 3) {
    CGFloat w = 116.3 * kWidthFactor;
    CGFloat h = 116.3 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 4) {
    CGFloat w = 178.5 * kWidthFactor;
    CGFloat h = 178.5 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 5) {
    CGFloat w = 116.3 * kWidthFactor;
    CGFloat h = 116.3 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 6) {
    CGFloat w = 116.3 * kWidthFactor;
    CGFloat h = 116.3 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 7) {
    CGFloat w = 116.3 * kWidthFactor;
    CGFloat h = 116.3 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 8) {
    CGFloat w = 116.3 * kWidthFactor;
    CGFloat h = 116.3 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 9) {
    CGFloat w = 116.3 * kWidthFactor;
    CGFloat h = 116.3 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 11) {
    CGFloat w = 165 * kWidthFactor;
    CGFloat h = 165 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 12) {
    CGFloat w = 240 * kWidthFactor;
    CGFloat h = 135 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
  if (_imageNum == 13) {
    CGFloat w = 135 * kWidthFactor;
    CGFloat h = 240 * kWidthFactor;
    [self.subviews enumerateObjectsUsingBlock:^(UIButton *btn, NSUInteger idx,
                                                BOOL *stop) {
      long rowIndex = idx / perRowImageCount;
      int columnIndex = idx % perRowImageCount;
      CGFloat x = columnIndex * (w + HZPhotoGroupImageMargin);
      CGFloat y = rowIndex * (h + HZPhotoGroupImageMargin);
      btn.layer.cornerRadius = 12 * kMainTemp;
      btn.frame = CGRectMake(x, y, w, h);
    }];
  }
}

- (void)tap:(UIGestureRecognizer *)gesture {
  if (self.isVideo) {
    if ([self.delegate respondsToSelector:@selector(photoGroupNine:
                                              didTapVideoWithVideoURL:)]) {
      [self.delegate photoGroupNine:self didTapVideoWithVideoURL:self.videoURL];
    }
    return;
  }
  FLAnimatedImageView *imageView = (FLAnimatedImageView *)gesture.view;
  HZPhotoBrowser *browser = [[HZPhotoBrowser alloc] init];
  browser.isFullWidthForLandScape = YES;
  browser.isNeedLandscape = YES;
  browser.sourceImagesContainerView = self;
  browser.currentImageIndex = (int)imageView.tag;
  browser.imageCount = self.urlArray.count;
  browser.delegate = self;
  [browser show];
}

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style {
  return CGSizeMake(365 * kWidthFactor, [NemoUtil getHZPhotoHeight:count
                                                             index:style]);
}
#pragma mark - photobrowser代理方法
- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser
    placeholderImageForIndex:(NSInteger)index {
  FLAnimatedImageView *imageView = (FLAnimatedImageView *)self.subviews[index];
  return imageView.image;
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser
    highQualityImageURLForIndex:(NSInteger)index {
  NSString *urlStr =
      [self.urlArray[index] stringByReplacingOccurrencesOfString:@"thumbnail"
                                                      withString:@"bmiddle"];
  return [NSURL URLWithString:urlStr];
}

@end
