#import <UIKit/UIKit.h>

@class HZPhotoGroupFull;

@protocol HZPhotoGroupFullDelegate <NSObject>
@optional
- (void)photoGroupFull:(HZPhotoGroupFull *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL;
- (void)photoGroupFull:(HZPhotoGroupFull *)photoGroup
    didTapImageAtIndex:(NSInteger)index;
@end

@interface HZPhotoGroupFull : UIView

@property(nonatomic, strong) NSArray<NSString *> *urlArray;
@property(nonatomic, weak) id<HZPhotoGroupFullDelegate> delegate;
@property(nonatomic, assign) BOOL isVideo;
@property(nonatomic, copy) NSString *videoURL;
@property(nonatomic, assign, readonly) NSInteger currentIndex;

- (void)getNum:(NSInteger)num index:(NSInteger)index;

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style;

@end
