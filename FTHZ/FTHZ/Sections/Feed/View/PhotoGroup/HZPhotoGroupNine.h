#import <UIKit/UIKit.h>

@class HZPhotoGroupNine;

@protocol HZPhotoGroupNineDelegate <NSObject>
@optional
- (void)photoGroupNine:(HZPhotoGroupNine *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL;
@end

@interface HZPhotoGroupNine : UIView

@property(nonatomic, strong) NSArray<NSString *> *urlArray;
@property(nonatomic, weak) id<HZPhotoGroupNineDelegate> delegate;
@property(nonatomic, assign) BOOL isVideo;
@property(nonatomic, copy) NSString *videoURL;

- (void)getNum:(NSInteger)num index:(NSInteger)index;

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style;

@end
