#import "HZPhotoGroupFull.h"
#import "FLAnimatedImageView.h"
#import "HZPhotoBrowser.h"
#import "HZPhotoBrowserConfig.h"
#import "UIButton+WebCache.h"
#import <AVKit/AVKit.h>
#import <FLAnimatedImage/FLAnimatedImageView.h>

@interface HZPhotoGroupFull () <HZPhotoBrowserDelegate, UIScrollViewDelegate>
@property(nonatomic, strong) UIScrollView *scrollView;
@property(nonatomic, strong) UIPageControl *pageControl;
@property(nonatomic, assign) NSInteger imageNum;
@property(nonatomic, assign) NSInteger imageIndex;
@property(nonatomic, assign) NSInteger currentIndex;
@property(nonatomic, strong) NSMutableArray<FLAnimatedImageView *> *imageViews;
@end

@implementation HZPhotoGroupFull

- (instancetype)init {
  self = [super init];
  if (self) {
    [self setupUI];
  }
  return self;
}

- (void)setupUI {
  self.imageViews = [[NSMutableArray alloc] init];

  self.scrollView = [[UIScrollView alloc] init];
  self.scrollView.pagingEnabled = YES;
  self.scrollView.showsHorizontalScrollIndicator = NO;
  self.scrollView.showsVerticalScrollIndicator = NO;
  self.scrollView.delegate = self;
  self.scrollView.bounces = NO;
  [self addSubview:self.scrollView];

  self.pageControl = [[UIPageControl alloc] init];
  self.pageControl.currentPageIndicatorTintColor = [UIColor whiteColor];
  self.pageControl.pageIndicatorTintColor = [UIColor colorWithWhite:1.0
                                                              alpha:0.3];
  self.pageControl.hidesForSinglePage = YES;
  [self addSubview:self.pageControl];
}

- (void)getNum:(NSInteger)num index:(NSInteger)index {
  _imageNum = num;
  _imageIndex = index;
}

- (void)setUrlArray:(NSArray<NSString *> *)urlArray {
  _urlArray = urlArray;
  [self setupImageViews];
}

- (void)setupImageViews {
  for (UIView *view in self.imageViews) {
    [view removeFromSuperview];
  }
  [self.imageViews removeAllObjects];

  if (self.urlArray.count == 0) {
    return;
  }

  self.pageControl.numberOfPages = self.urlArray.count;
  self.pageControl.currentPage = 0;
  self.currentIndex = 0;

  for (NSInteger i = 0; i < self.urlArray.count; i++) {
    FLAnimatedImageView *imageView = [[FLAnimatedImageView alloc] init];
    imageView.userInteractionEnabled = YES;
    imageView.contentMode = UIViewContentModeScaleAspectFill;
    imageView.clipsToBounds = YES;
    imageView.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];

    NSString *urlString = self.urlArray[i];
    [imageView sd_setImageWithURL:[NemoUtil getUrlWithUserPictaure:urlString]
                 placeholderImage:KImage_name(@"empty")];

    imageView.tag = i;

    UITapGestureRecognizer *tap =
        [[UITapGestureRecognizer alloc] initWithTarget:self
                                                action:@selector(tap:)];
    [imageView addGestureRecognizer:tap];

    if (i == 0 && self.isVideo) {
      UIView *playButtonBg = [[UIView alloc] init];
      playButtonBg.backgroundColor =
          [[UIColor blackColor] colorWithAlphaComponent:0.2];
      playButtonBg.layer.cornerRadius = 10 * kWidthFactor;
      [imageView addSubview:playButtonBg];

      [playButtonBg mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(imageView).offset(-8 * kWidthFactor);
        make.bottom.equalTo(imageView).offset(-8 * kWidthFactor);
        make.width.height.mas_equalTo(20 * kWidthFactor);
      }];

      CAShapeLayer *circleLayer = [CAShapeLayer layer];
      UIBezierPath *circlePath =
          [UIBezierPath bezierPathWithArcCenter:CGPointMake(10 * kWidthFactor,
                                                            10 * kWidthFactor)
                                         radius:8 * kWidthFactor
                                     startAngle:0
                                       endAngle:2 * M_PI
                                      clockwise:YES];
      circleLayer.path = circlePath.CGPath;
      circleLayer.strokeColor =
          [[UIColor whiteColor] colorWithAlphaComponent:0.8].CGColor;
      circleLayer.fillColor = [UIColor clearColor].CGColor;
      circleLayer.lineWidth = 1.5;
      [playButtonBg.layer addSublayer:circleLayer];

      CAShapeLayer *triangleLayer = [CAShapeLayer layer];
      UIBezierPath *trianglePath = [UIBezierPath bezierPath];
      [trianglePath
          moveToPoint:CGPointMake(8 * kWidthFactor, 6 * kWidthFactor)];
      [trianglePath
          addLineToPoint:CGPointMake(8 * kWidthFactor, 14 * kWidthFactor)];
      [trianglePath
          addLineToPoint:CGPointMake(14 * kWidthFactor, 10 * kWidthFactor)];
      [trianglePath closePath];
      triangleLayer.path = trianglePath.CGPath;
      triangleLayer.fillColor =
          [[UIColor whiteColor] colorWithAlphaComponent:0.8].CGColor;
      [playButtonBg.layer addSublayer:triangleLayer];
    }

    [self.scrollView addSubview:imageView];
    [self.imageViews addObject:imageView];
  }

  [self setNeedsLayout];
}

- (void)layoutSubviews {
  [super layoutSubviews];

  CGFloat width = self.bounds.size.width;
  CGFloat height = self.bounds.size.height;

  self.scrollView.frame = CGRectMake(0, 0, width, height);
  self.scrollView.contentSize = CGSizeMake(width * self.urlArray.count, height);

  for (NSInteger i = 0; i < self.imageViews.count; i++) {
    FLAnimatedImageView *imageView = self.imageViews[i];
    imageView.frame = CGRectMake(i * width, 0, width, height);
  }

  CGFloat pageControlHeight = 20;
  self.pageControl.frame =
      CGRectMake(0, height - pageControlHeight - 10, width, pageControlHeight);
}

- (void)tap:(UIGestureRecognizer *)gesture {
  if (self.isVideo) {
    if ([self.delegate respondsToSelector:@selector(photoGroupFull:
                                              didTapVideoWithVideoURL:)]) {
      [self.delegate photoGroupFull:self didTapVideoWithVideoURL:self.videoURL];
    }
    return;
  }

  FLAnimatedImageView *imageView = (FLAnimatedImageView *)gesture.view;
  NSInteger index = imageView.tag;

  if ([self.delegate respondsToSelector:@selector(photoGroupFull:
                                              didTapImageAtIndex:)]) {
    [self.delegate photoGroupFull:self didTapImageAtIndex:index];
  }

  HZPhotoBrowser *browser = [[HZPhotoBrowser alloc] init];
  browser.isFullWidthForLandScape = YES;
  browser.isNeedLandscape = YES;
  browser.sourceImagesContainerView = self.scrollView;
  browser.currentImageIndex = (int)index;
  browser.imageCount = self.urlArray.count;
  browser.delegate = self;
  [browser show];
}

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style {
  CGFloat width = [UIScreen mainScreen].bounds.size.width;
  CGFloat height = width * 4.0 / 5.0;
  return CGSizeMake(width, height);
}

#pragma mark - UIScrollViewDelegate

- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
  CGFloat pageWidth = scrollView.frame.size.width;
  NSInteger currentPage =
      floor((scrollView.contentOffset.x - pageWidth / 2) / pageWidth) + 1;

  self.pageControl.currentPage = currentPage;
  self.currentIndex = currentPage;
}

#pragma mark - HZPhotoBrowserDelegate

- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser
    placeholderImageForIndex:(NSInteger)index {
  if (index < self.imageViews.count) {
    FLAnimatedImageView *imageView = self.imageViews[index];
    return imageView.image;
  }
  return nil;
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser
    highQualityImageURLForIndex:(NSInteger)index {
  if (index < self.urlArray.count) {
    NSString *urlStr =
        [self.urlArray[index] stringByReplacingOccurrencesOfString:@"thumbnail"
                                                        withString:@"bmiddle"];
    return [NSURL URLWithString:urlStr];
  }
  return nil;
}

@end
