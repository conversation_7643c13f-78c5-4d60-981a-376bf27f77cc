#import <UIKit/UIKit.h>

@class HZPhotoGroupAbbreviation;

@protocol HZPhotoGroupAbbreviationDelegate <NSObject>
@optional
- (void)photoGroupAbbreviation:(HZPhotoGroupAbbreviation *)photoGroup
    didTapVideoWithVideoURL:(NSString *)videoURL;
@end

@interface HZPhotoGroupAbbreviation : UIView

@property(nonatomic, strong) NSArray<NSString *> *urlArray;
@property(nonatomic, weak) id<HZPhotoGroupAbbreviationDelegate> delegate;
@property(nonatomic, assign) BOOL isVideo;
@property(nonatomic, copy) NSString *videoURL;

- (void)getNum:(NSInteger)num index:(NSInteger)index;

+ (CGSize)sizeWithImageCount:(NSUInteger)count displayStyle:(NSInteger)style;

@end
