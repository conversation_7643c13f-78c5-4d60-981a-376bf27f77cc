#import "AffairListModel.h"
#import "FTHZMusicPlayerView.h"
#import "FTHZToolbarButton.h"
#import "FlatButton.h"
#import "HZPhotoGroupAbbreviation.h"
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface FTHZFeedCardContentViewModel : NSObject

@property(nonatomic, copy, readonly, nullable) NSString *feedID;

@property(nonatomic, copy, readonly, nullable) NSString *nickName;

@property(nonatomic, copy, readonly, nullable) NSString *city;

@property(nonatomic, copy, readonly) NSString *content;

@property(nonatomic, strong, readonly, nullable) NSArray<NSString *> *photoURLs;

@property(nonatomic, assign, readonly) NSInteger photoStyle;

@property(nonatomic, strong, readonly, nullable) MusicInfoData *musicInfo;

@property(nonatomic, strong, readonly, nullable) NSString *tag;

@property(nonatomic, assign) NSInteger upvoteCount;

@property(nonatomic, assign) BOOL isUpvoted;

@property(nonatomic, assign) NSInteger commentCount;

@property(nonatomic, assign, readonly) BOOL enablePasteContent;

@property(nonatomic, strong, readonly) RACCommand *upvoteAction;

@property(nonatomic, strong, readonly) RACCommand *commentAction;

@property(nonatomic, strong, readonly) RACCommand *longpressAction;

- (instancetype)initWithModel:(DynamicModelResult *)model
           enablePasteContent:(BOOL)enablePasteContent;

@end

/**
 Feed流的卡片
 */
@interface FTHZFeedCardContentView : UIView

@property(nonatomic, strong, readonly) UILabel *nameLabel;

@property(nonatomic, strong, readonly)
    UILongPressGestureRecognizer *contentLongpressGesture;

@property(nonatomic, strong, readonly) UILabel *contentLabel;

@property(nonatomic, strong, readonly) HZPhotoGroupAbbreviation *photoGroupView;

@property(nonatomic, strong, readonly) FTHZMusicPlayerView *musicPlayerView;

@property(nonatomic, strong, readonly) FlatButton *tagButton;

@property(nonatomic, strong, readonly) FTHZToolbarButton *upvoteButton;

@property(nonatomic, strong, readonly) FTHZToolbarButton *commentButton;

- (void)setUserName:(NSString *)userName city:(NSString *_Nullable)city;

- (void)setContent:(NSString *)content;

- (void)setPhotoWithURLs:(NSArray *)photoURLs displayStyle:(NSInteger)style;

- (void)setMusicInfo:(MusicInfoData *_Nullable)info
           bindingID:(NSString *_Nullable)ID;

- (void)setTagString:(NSString *_Nullable)tag;

- (void)setUpvoteCount:(NSInteger)count
             isUpvoted:(BOOL)isUpvoted
              animated:(BOOL)animated;

- (void)setCommentCount:(NSInteger)count
            isCommented:(BOOL)isCommented
               animated:(BOOL)animated;

- (void)bindWithModel:(FTHZFeedCardContentViewModel *)model;

- (void)showPasteOption;

+ (CGFloat)heightWithContentWidth:(CGFloat)contentWidth
                          content:(NSString *)content
                         hasMusic:(BOOL)hasMusic
                       photoCount:(NSUInteger)photoCount
                photoDisplayStyle:(NSInteger)displayStyle;

@end

NS_ASSUME_NONNULL_END
