#import "FTHZFeedCardContentView.h"
#import "NSAttributedString+BoundingRect.h"

static const CGFloat kNameLabelPaddingTop = 0;
static const CGFloat kPhotoViewPaddingTop = 12;
static const CGFloat kMusicPlayerViewPaddingTop = 12;
static const CGFloat kMusicPlayerViewHeight = 40;
static const CGFloat kActionButtonMarginTop = 12;
static const CGFloat kActionButtonPaddingBottom = 12;

static const NSTimeInterval kButtonAnimationDuration = 0.25;

@implementation FTHZFeedCardContentViewModel

- (instancetype)initWithModel:(DynamicModelResult *)model
           enablePasteContent:(BOOL)enablePasteContent {
  self = [super init];
  if (self) {
    NSString *feedID = [model.affair.aid copy];
    NSString *userID = [model.user.uid copy];

    _feedID = feedID;
    _nickName = [model.user.nickname copy];
    _city = [model.user.city copy];
    _content = [(model.affair.content ?: @"") copy];
    _photoURLs = [model.affair photoURLs];
    _musicInfo = model.affair.musicContent;
    _tag = [model.affair.tagName copy];
    _upvoteCount = [model.affair upvoteCount];
    _isUpvoted = [model.affair upvoted];
    _commentCount = [model.affair commentCount];
    _photoStyle = [model.affair.imageType integerValue];
    _enablePasteContent = enablePasteContent;
    @weakify(self);
    _upvoteAction = [[RACCommand alloc]
        initWithSignalBlock:^RACSignal *_Nonnull(id _Nullable input) {
          return [[FTHZBusiness toggleUpvoteWithFeedID:feedID authorID:userID]
              doNext:^(NSNumber *_Nullable x) {
                @strongify(self);
                self.isUpvoted = !self.isUpvoted;
                self.upvoteCount += self.isUpvoted ? 1 : -1;
              }];
        }];

    _commentAction = [[RACCommand alloc]
        initWithSignalBlock:^RACSignal *_Nonnull(id _Nullable input) {
          return [RACSignal createSignal:^RACDisposable *_Nullable(
                                id<RACSubscriber> _Nonnull subscriber) {
            [Coordinator openFeedDetailWithModel:model autoShowComment:YES];
            [subscriber sendCompleted];
            return nil;
          }];
        }];
    _longpressAction = [[RACCommand alloc]
        initWithSignalBlock:^RACSignal *_Nonnull(id _Nullable input) {
          return [RACSignal createSignal:^RACDisposable *_Nullable(
                                id<RACSubscriber> _Nonnull subscriber) {
            [input showPasteOption];
            [subscriber sendCompleted];
            return nil;
          }];
        }];
  }
  return self;
}

@end

@implementation FTHZFeedCardContentView

- (instancetype)initWithFrame:(CGRect)frame {
  self = [super initWithFrame:frame];
  if (self) {
    _nameLabel = [[UILabel alloc] init];
    _contentLabel = [[UILabel alloc] init];
    _photoGroupView = [[HZPhotoGroupAbbreviation alloc] init];
    _musicPlayerView = [[FTHZMusicPlayerView alloc] init];
    _tagButton = [[FlatButton alloc] init];
    _upvoteButton = [FTHZToolbarButton button];
    _commentButton = [FTHZToolbarButton button];
    _contentLongpressGesture = [[UILongPressGestureRecognizer alloc] init];
    [self setupUI];
  }
  return self;
}

- (void)setupUI {
  [self addGestureRecognizer:self.contentLongpressGesture];
  self.nameLabel.numberOfLines = 1;
  [self addSubview:self.nameLabel];

  self.contentLabel.textColor = KColor_Gray;
  self.contentLabel.font = SourceHanSerifRegularFont(14);
  self.contentLabel.numberOfLines = 0;
  [self addSubview:self.contentLabel];

  [self addSubview:self.photoGroupView];
  [self addSubview:self.musicPlayerView];
  [self addSubview:self.tagButton];
  [self addSubview:self.upvoteButton];
  [self addSubview:self.commentButton];
}

- (void)layoutSubviews {
  [super layoutSubviews];

  self.nameLabel.top = kNameLabelPaddingTop;
  self.nameLabel.left = 0;
  self.nameLabel.height = [[self class] nameLabelBigFont].lineHeight;
  self.nameLabel.width = CGRectGetWidth(self.bounds);

  self.contentLabel.width = CGRectGetWidth(self.bounds);
  self.contentLabel.height = [[[NSAttributedString alloc]
      initWithString:self.contentLabel.text ?: @""
          attributes:[[self class] contentAttributes]]
      suggestHeightWithWidthLimit:self.contentLabel.width];
  self.contentLabel.top =
      self.nameLabel.bottom +
      [[self class] contentLabelMarginTopWithImageCount:self.photoGroupView
                                                            .urlArray.count];
  self.contentLabel.left = 0;
  UIView *upperView = self.contentLabel;

  if (!self.photoGroupView.isHidden) {
    self.photoGroupView.left = 0;
    self.photoGroupView.top = upperView.bottom + kPhotoViewPaddingTop;
    upperView = self.photoGroupView;
  }

  if (!self.musicPlayerView.isHidden) {
    self.musicPlayerView.left = 8;
    self.musicPlayerView.top = upperView.bottom + 12;
    self.musicPlayerView.width =
        CGRectGetMaxX(self.bounds) - 24 - self.musicPlayerView.left;
    self.musicPlayerView.height = 40;
    upperView = self.musicPlayerView;
  }

  self.commentButton.right = CGRectGetMaxX(self.bounds);
  self.commentButton.top = upperView.bottom + kActionButtonMarginTop;

  self.upvoteButton.right = self.commentButton.left;
  self.upvoteButton.centerY = self.commentButton.centerY;
}

- (void)setUserName:(NSString *)userName city:(NSString *_Nullable)city {
  UIFont *mediumFont = [[self class] nameLabelBigFont];
  UIFont *regularFont = SourceHanSerifRegularFont(12);
  NSMutableAttributedString *userDescription =
      [[NSMutableAttributedString alloc]
          initWithString:userName
              attributes:@{
                NSFontAttributeName : mediumFont,
                NSForegroundColorAttributeName : KColor_Black,
              }];
  if (city) {
    [userDescription
        appendAttributedString:[[NSAttributedString alloc]
                                   initWithString:[NSString
                                                      stringWithFormat:@" %@",
                                                                       city]
                                       attributes:@{
                                         NSFontAttributeName : regularFont,
                                         NSForegroundColorAttributeName :
                                             KColor_Gray,
                                       }]];
  }
  self.nameLabel.attributedText = [userDescription copy];
  [self.nameLabel sizeToFit];
  [self setNeedsLayout];
}

- (void)setContent:(NSString *)content {
  self.contentLabel.text = content;
  [self setNeedsLayout];
}

- (void)setPhotoWithURLs:(NSArray *)photoURLs displayStyle:(NSInteger)style {
  self.photoGroupView.urlArray = photoURLs;
  self.photoGroupView.hidden = photoURLs.count == 0;
  [self.photoGroupView getNum:photoURLs.count index:style];
  self.photoGroupView.size =
      [HZPhotoGroupAbbreviation sizeWithImageCount:photoURLs.count
                                      displayStyle:style];
  [self setNeedsLayout];
}

- (void)setMusicInfo:(MusicInfoData *_Nullable)info
           bindingID:(NSString *_Nullable)ID {
  [self.musicPlayerView setMusicInfo:info uuid:ID autoTrace:info != nil];
  self.musicPlayerView.hidden = info == nil;
  [self setNeedsLayout];
}

- (void)setTagString:(NSString *_Nullable)tag {
  [self.tagButton setTitle:tag forState:UIControlStateNormal];
  self.tagButton.hidden = tag == nil;
  [self setNeedsLayout];
}

- (void)setCommentCount:(NSInteger)count
            isCommented:(BOOL)isCommented
               animated:(BOOL)animated {
  void (^action)(void) = ^{
    [self.commentButton setImage:KImage_name(@"Comment")];
    [self.commentButton setText:[NSString stringWithFormat:@"%li", count]];
  };
  if (animated) {
    [UIView animateWithDuration:kButtonAnimationDuration
                     animations:^{
                       action();
                     }];
  } else {
    action();
  }
}

- (void)setUpvoteCount:(NSInteger)count
             isUpvoted:(BOOL)isUpvoted
              animated:(BOOL)animated {
  void (^action)(void) = ^{
    [self.upvoteButton setImage:KImage_name(isUpvoted ? @"like" : @"unlike")];
    [self.upvoteButton setText:[NSString stringWithFormat:@"%li", count]];
  };
  if (animated) {
    [UIView animateWithDuration:kButtonAnimationDuration
                     animations:^{
                       action();
                     }];
  } else {
    action();
  }
}

+ (UIFont *)nameLabelBigFont {
  return SourceHanSerifMediumFont(16);
}

+ (NSDictionary *)contentAttributes {
  static NSDictionary *attr;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    attr = @{
      NSFontAttributeName : SourceHanSerifRegularFont(14),
      NSForegroundColorAttributeName : KColor_Gray,
    };
  });
  return attr;
}

+ (CGFloat)contentLabelMarginTopWithImageCount:(NSUInteger)count {
  return count > 0 ? 8 : 12;
}

+ (CGFloat)heightWithContentWidth:(CGFloat)contentWidth
                          content:(NSString *)content
                         hasMusic:(BOOL)hasMusic
                       photoCount:(NSUInteger)photoCount
                photoDisplayStyle:(NSInteger)displayStyle {
  CGFloat totalHeight = kNameLabelPaddingTop +
                        [self nameLabelBigFont].lineHeight +
                        [self contentLabelMarginTopWithImageCount:photoCount];
  NSAttributedString *contentAttr =
      [[NSAttributedString alloc] initWithString:content ?: @""
                                      attributes:[self contentAttributes]];
  CGFloat contentHeight =
      [contentAttr suggestHeightWithWidthLimit:contentWidth];
  totalHeight += contentHeight;
  if (photoCount > 0) {
    totalHeight += kPhotoViewPaddingTop + [HZPhotoGroupAbbreviation
                                              sizeWithImageCount:photoCount
                                                    displayStyle:displayStyle]
                                              .height;
  }
  if (hasMusic) {
    totalHeight += kMusicPlayerViewPaddingTop + kMusicPlayerViewHeight;
  }
  totalHeight += [FTHZToolbarButton defaultSize].height +
                 kActionButtonMarginTop + kActionButtonPaddingBottom;
  return totalHeight;
}

- (void)bindWithModel:(FTHZFeedCardContentViewModel *)model {
  @weakify(self);
  [self addSubscription:[RACObserve(model, commentCount)
                            subscribeNext:^(NSNumber *_Nullable x) {
                              @strongify(self);
                              [self setCommentCount:[x integerValue]
                                        isCommented:NO
                                           animated:NO];
                            }]];

  RACSignal *upvoteCountSignal = RACObserve(model, upvoteCount);
  RACSignal *upvotedSignal = RACObserve(model, isUpvoted);
  [self addSubscription:[[RACSignal
                            combineLatest:@[ upvoteCountSignal, upvotedSignal ]]
                            subscribeNext:^(RACTuple *_Nullable x) {
                              NSInteger count = [x[0] integerValue];
                              BOOL isUpvoted = [x[1] boolValue];
                              @strongify(self);
                              [self setUpvoteCount:count
                                         isUpvoted:isUpvoted
                                          animated:YES];
                            }]];
  [self setUserName:model.nickName city:model.city];
  [self setContent:model.content];
  [self setPhotoWithURLs:model.photoURLs displayStyle:model.photoStyle];
  [self setMusicInfo:model.musicInfo bindingID:model.feedID];
  [self setTagString:model.tag];
  self.upvoteButton.rac_command = model.upvoteAction;
  self.commentButton.rac_command = model.commentAction;
  self.contentLongpressGesture.enabled = model.enablePasteContent;
  [self addSubscription:[[self.contentLongpressGesture rac_gestureSignal]
                            subscribeNext:^(
                                __kindof UIGestureRecognizer *_Nullable x) {
                              switch (x.state) {
                              case UIGestureRecognizerStateBegan: {
                                @strongify(self);
                                [model.longpressAction execute:self];
                              } break;
                              default:
                                break;
                              }
                            }]];
}

- (BOOL)canBecomeFirstResponder {
  return YES;
}

- (BOOL)canResignFirstResponder {
  return YES;
}

- (void)showPasteOption {
  if (![self becomeFirstResponder]) {
    return;
  }
  UIMenuController *menu = [UIMenuController sharedMenuController];
  menu.arrowDirection = UIMenuControllerArrowDefault;
  [menu setTargetRect:self.contentLabel.frame inView:self];
  UIMenuItem *item = [[UIMenuItem alloc] initWithTitle:@"复制"
                                                action:@selector(copyContent)];
  menu.menuItems = @[ item ];
  [menu setMenuVisible:YES animated:YES];
}

- (void)copyContent {
  [UIPasteboard generalPasteboard].string = self.contentLabel.text ?: @"";
  [self resignFirstResponder];
}

@end
